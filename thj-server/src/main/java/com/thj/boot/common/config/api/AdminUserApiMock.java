package com.thj.boot.common.config.api;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.AdminUserRespDTO;
import com.thj.boot.module.system.api.user.dto.UserCreateReqDTO;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * AdminUserApi的模拟实现
 */
@Service
@Primary
@ConditionalOnProperty(name = "spring.redis.enabled", havingValue = "false", matchIfMissing = false)
public class AdminUserApiMock implements AdminUserApi {

    @Override
    public AdminUserRespDTO getUser(Long id) {
        AdminUserRespDTO dto = new AdminUserRespDTO();
        dto.setId(id);
        dto.setNickname("模拟用户");
        dto.setStatus(0);
        return dto;
    }

    @Override
    public UserRespDTO getAdminUser(UserCreateReqDTO createReqDTO) {
        UserRespDTO dto = new UserRespDTO();
        // 只设置基本字段，避免编译错误
        return dto;
    }

    @Override
    public List<UserRespDTO> adminUserList(UserRespDTO userRespDTO) {
        List<UserRespDTO> list = new ArrayList<>();
        UserRespDTO dto = new UserRespDTO();
        // 只设置基本字段，避免编译错误
        list.add(dto);
        return list;
    }

    @Override
    public void updateAdminUser(UserCreateReqDTO createReqDTO) {
        // 模拟实现
    }

    @Override
    public List<UserRespDTO> selectUserJoinRole() {
        List<UserRespDTO> list = new ArrayList<>();
        UserRespDTO dto = new UserRespDTO();
        // 只设置基本字段，避免编译错误
        list.add(dto);
        return list;
    }

    @Override
    public UserRespDTO getAdminUserInfo(Long id) {
        UserRespDTO dto = new UserRespDTO();
        // 只设置基本字段，避免编译错误
        return dto;
    }

    @Override
    public List<UserRespDTO> getUserByRole(String roleCode, String deptId) {
        List<UserRespDTO> list = new ArrayList<>();
        UserRespDTO dto = new UserRespDTO();
        // 只设置基本字段，避免编译错误
        list.add(dto);
        return list;
    }

    @Override
    public PageResult<UserRespDTO> getUserByRolePage(String roleCode, Long userId, String deptId, Integer pageNo, Integer size) {
        List<UserRespDTO> list = new ArrayList<>();
        UserRespDTO dto = new UserRespDTO();
        // 只设置基本字段，避免编译错误
        list.add(dto);
        return new PageResult<>(list, 1L);
    }
}
