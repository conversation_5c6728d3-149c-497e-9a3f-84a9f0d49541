package com.thj.boot.module.business.service.arrangeclasses;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Maps;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.DateUtils;
import com.thj.boot.common.utils.redis.RedisUtils;
import com.thj.boot.module.business.controller.admin.arrangeclasses.event.TeamLogEvent;
import com.thj.boot.module.business.controller.admin.arrangeclasses.vo.BrainPowerCreateReqVO;
import com.thj.boot.module.business.controller.admin.arrangeclasses.vo.BrainPowerRespVO;
import com.thj.boot.module.business.controller.admin.drainteam.vo.DialyzBudgetRespVO;
import com.thj.boot.module.business.controller.admin.drainteam.vo.DrainTeamAlreadyVO;
import com.thj.boot.module.business.convert.arrangeclasses.ArrangeClassesConvert;
import com.thj.boot.module.business.convert.facility.FacilityConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.dal.datado.dialysisdetection.DialysisDetectionDO;
import com.thj.boot.module.business.dal.datado.dialysismanager.DialysisManagerDO;
import com.thj.boot.module.business.dal.datado.dialysisprotocol.DialysisProtocolDO;
import com.thj.boot.module.business.dal.datado.dialyzeoption.DialyzeOptionDO;
import com.thj.boot.module.business.dal.datado.diseasereason.DiseaseReasonDO;
import com.thj.boot.module.business.dal.datado.disinfectionplan.DisinfectionPlanDO;
import com.thj.boot.module.business.dal.datado.dryweight.DryWeightDO;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.dal.datado.facilityname.FacilityNameDO;
import com.thj.boot.module.business.dal.datado.facilitysubarea.FacilitySubareaDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.teamlog.TeamLogDO;
import com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper;
import com.thj.boot.module.business.dal.mapper.dialysisadvice.DialysisAdviceMapper;
import com.thj.boot.module.business.dal.mapper.dialysisdetection.DialysisDetectionMapper;
import com.thj.boot.module.business.dal.mapper.dialysismanager.DialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.dialysisprotocol.DialysisProtocolMapper;
import com.thj.boot.module.business.dal.mapper.dialyzeoption.DialyzeOptionMapper;
import com.thj.boot.module.business.dal.mapper.diseasereason.DiseaseReasonMapper;
import com.thj.boot.module.business.dal.mapper.disinfectionplan.DisinfectionPlanMapper;
import com.thj.boot.module.business.dal.mapper.dryweight.DryWeightMapper;
import com.thj.boot.module.business.dal.mapper.facility.FacilityMapper;
import com.thj.boot.module.business.dal.mapper.facilitymanager.FacilityManagerMapper;
import com.thj.boot.module.business.dal.mapper.facilityname.FacilityNameMapper;
import com.thj.boot.module.business.dal.mapper.facilitysubarea.FacilitySubareaMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.hisconsumables.HisConsumablesMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.*;
import com.thj.boot.module.business.pojo.facility.vo.FacilityPageReqVO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityRespVO;
import com.thj.boot.module.business.pojo.facilitysubarea.vo.FacilitySubareaPageReqVO;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.UserCreateReqDTO;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 新排班 Service 实现类
 *
 * <AUTHOR>
 */
@Service("arrangeClassesService")
@Validated
@Slf4j
@ConditionalOnProperty(name = "spring.redis.enabled", havingValue = "true", matchIfMissing = true)
public class ArrangeClassesServiceImpl2 implements ArrangeClassesService {

    @Resource
    private ArrangeClassesMapper arrangeClassesMapper;

    @Resource
    private FacilityMapper facilityMapper;

    @Resource
    private FacilitySubareaMapper subareaMapper;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private FacilityManagerMapper facilityManagerMapper;

    @Resource
    private DialyzeOptionMapper dialyzeOptionMapper;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DryWeightMapper dryWeightMapper;

    @Resource
    private DialysisManagerMapper dialysisManagerMapper;

    @Resource
    private DiseaseReasonMapper diseaseReasonMapper;

    @Resource
    private DisinfectionPlanMapper disinfectionPlanMapper;

    @Resource
    private FacilityNameMapper facilityNameMapper;

    @Resource
    private FacilitySubareaMapper facilitySubareaMapper;

    @Resource
    private DialysisAdviceMapper dialysisAdviceMapper;

    @Resource
    private DialysisProtocolMapper dialysisProtocolMapper;

    @Resource
    private HisConsumablesMapper hisConsumablesMapper;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private DialysisDetectionMapper dialysisDetectionMapper;

    @Autowired
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createArrangeClasses(ArrangeClassesCreateReqVO createReqVO, HttpServletRequest request) {
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:createArrangeClasses:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {

                ArrangeClassesDO arrangeClasses = ArrangeClassesConvert.INSTANCE.convert(createReqVO);
                DialyzeOptionDO dialyzeOptionDO = dialyzeOptionMapper.selectById(createReqVO.getOptionId());
                if (!StringUtils.isEmpty(dialyzeOptionDO)) {
                    arrangeClasses.setProtocolId(dialyzeOptionDO.getProtocolId());
                }
                long loginIdAsLong = StpUtil.getLoginIdAsLong();
                if (loginIdAsLong != 1 && createReqVO.getTempType() == 0) {
                    if (arrangeClasses.getClassesTime().before(DateUtil.beginOfDay(DateUtil.date()))) {
                        throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK);
                    }
                }
                List<DisinfectionPlanDO> disinfectionPlanDOS = disinfectionPlanMapper.selectList(new LambdaQueryWrapperX<DisinfectionPlanDO>()
                        .eqIfPresent(DisinfectionPlanDO::getFacilityId, arrangeClasses.getFacilityId())
                        .eqIfPresent(DisinfectionPlanDO::getWeekDay, arrangeClasses.getDayState())
                        .eqIfPresent(DisinfectionPlanDO::getDisinfectionTime, arrangeClasses.getWeekState()));
                FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, arrangeClasses.getFacilityId());
                if (CollectionUtil.isEmpty(disinfectionPlanDOS)) {
                    throw new ServiceException(GlobalErrorCodeConstants.NO_PLAN);
                }
                if (facilityManagerDO == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.NO_PLAN);
                }
                ArrangeClassesDO classesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getTempType, arrangeClasses.getTempType())
                        .eqIfPresent(ArrangeClassesDO::getPatientId, arrangeClasses.getPatientId())
                        .eq(0 == arrangeClasses.getTempType(), ArrangeClassesDO::getClassesTime, arrangeClasses.getClassesTime())
                        .eqIfPresent(ArrangeClassesDO::getWeekState, arrangeClasses.getWeekState()));
                ArrangeClassesDO newClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getTempType, arrangeClasses.getTempType())
                        .eqIfPresent(ArrangeClassesDO::getDayState, arrangeClasses.getDayState())
                        .eqIfPresent(ArrangeClassesDO::getFacilityId, arrangeClasses.getFacilityId())
                        .eqIfPresent(ArrangeClassesDO::getFacilitySubareaId, arrangeClasses.getFacilitySubareaId())
                        .eq(0 == arrangeClasses.getTempType(), ArrangeClassesDO::getClassesTime, arrangeClasses.getClassesTime())
                        .eqIfPresent(ArrangeClassesDO::getWeekState, arrangeClasses.getWeekState()));
                if (classesDO != null && 2 < classesDO.getState()) {
                    throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK);
                }
                PatientDO patientDO = patientMapper.selectById(arrangeClasses.getPatientId());
                if (patientDO != null) {
                    arrangeClasses.setPatientName(patientDO.getName());
                    arrangeClasses.setPatientSrouce(patientDO.getPatientSource());
                    arrangeClasses.setInfects(patientDO.getInfect());
                    arrangeClasses.setLabels(patientDO.getLabels());
                }
                DictDataRespDTO dictData = dictDataApi.getDictData("dialyze_way", createReqVO.getDialysisValue());
                arrangeClasses.setDialysisName(dictData == null ? null : dictData.getLabel());
                if (!StringUtils.isEmpty(createReqVO.getDialysisName())) {
                    arrangeClasses.setDialysisName(createReqVO.getDialysisName());
                }
                FacilityDO facilityDO = facilityMapper.selectById(createReqVO.getFacilityId());
                arrangeClasses.setFacilityName(facilityDO == null ? null : facilityDO.getCode());
                FacilitySubareaDO facilitySubareaDO = subareaMapper.selectById(createReqVO.getFacilitySubareaId());
                arrangeClasses.setFaciitySubareaName(facilitySubareaDO == null ? null : facilitySubareaDO.getName());
                if (classesDO == null && newClassesDO == null) {
                    if (0 != arrangeClasses.getTempType()) {
                        arrangeClasses.setClassesTime(null);
                    }
                    arrangeClassesMapper.insert(arrangeClasses);
                    TeamLogEvent teamLogEvent = new TeamLogEvent("");
                    TeamLogDO teamLogDO = new TeamLogDO();
                    teamLogDO.setAdjustTime(DateUtil.date());
                    teamLogDO.setContent("新增");
                    teamLogDO.setPatientId(arrangeClasses.getPatientId());
                    teamLogDO.setPatientName(arrangeClasses.getPatientName());
                    teamLogDO.setDialyzeName(arrangeClasses.getDialysisName());
                    teamLogDO.setFacilityName(arrangeClasses.getFacilityName());
                    Week week = DateUtil.dayOfWeekEnum(arrangeClasses.getClassesTime());
                    String weekStr = week.toChinese("周");
                    StringBuilder sb = new StringBuilder();
                    sb.append(weekStr).append(arrangeClasses.getClassesTime() != null ? DateUtil.format(arrangeClasses.getClassesTime(), DatePattern.NORM_DATE_PATTERN) : "");
                    teamLogDO.setTeamTime(sb.toString());
                    DictDataRespDTO arrangeClassesPeriodTime = dictDataApi.getDictData("arrangeClassesPeriodTime", arrangeClasses.getDayState());
                    teamLogDO.setWeekFlag(arrangeClassesPeriodTime != null ? arrangeClassesPeriodTime.getLabel() : null);
                    teamLogEvent.setTeamLogDO(teamLogDO);
                    applicationContext.publishEvent(teamLogEvent);
                } else {
                    if (0 != arrangeClasses.getTempType()) {
                        arrangeClasses.setClassesTime(null);
                        updateClassesPosition(arrangeClasses, classesDO, newClassesDO);
                    } else {
                        updateClassesPosition(arrangeClasses, classesDO, newClassesDO);
                    }

                }
            } else {
                return;
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("新增排班信息异常:{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    private void updateClassesPosition(ArrangeClassesDO arrangeClasses, ArrangeClassesDO classesDO, ArrangeClassesDO newClassesDO) {
        if (classesDO == null) {
            updateClassesDO(arrangeClasses);
        } else {
            arrangeClasses.setState(classesDO.getState());
            arrangeClassesMapper.deleteById(classesDO.getId());
            if (newClassesDO != null) {
                if (newClassesDO.getPatientId().equals(classesDO.getPatientId())) {
                    arrangeClasses.setId(null);
                    arrangeClassesMapper.insert(arrangeClasses);
                }
                updateClassesDO(arrangeClasses);
            } else {
                arrangeClasses.setId(null);
                arrangeClassesMapper.insert(arrangeClasses);
            }
        }

    }

    private void updateClassesDO(ArrangeClassesDO arrangeClasses) {
        arrangeClassesMapper.update(arrangeClasses, new LambdaUpdateWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getTempType, arrangeClasses.getTempType())
                .eq(ArrangeClassesDO::getDayState, arrangeClasses.getDayState())
                .eq(ArrangeClassesDO::getFacilityId, arrangeClasses.getFacilityId())
                .eq(ArrangeClassesDO::getFacilitySubareaId, arrangeClasses.getFacilitySubareaId())
                .eq(0 == arrangeClasses.getTempType(), ArrangeClassesDO::getClassesTime, arrangeClasses.getClassesTime())
                .eq(ArrangeClassesDO::getWeekState, arrangeClasses.getWeekState()));
    }

    @Override
    public void updateArrangeClasses(ArrangeClassesUpdateReqVO updateReqVO) {
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteArrangeClasses(ArrangeClassesCreateReqVO createReqVO) {
        long userId = StpUtil.getLoginIdAsLong();
        if (userId != 1 && createReqVO.getTempType() == 0) {
            if (createReqVO.getClassesTime().before(DateUtil.beginOfDay(new Date()))) {
                throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK_DEL);
            }
        }
        // 删除
        //ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectById(createReqVO.getId());
        //if (arrangeClassesDO != null && arrangeClassesDO.getState() > 1) {
        //    throw new ServiceException(GlobalErrorCodeConstants.DIALYSIS_NOW);
        //}
        arrangeClassesMapper.deleteById(createReqVO);
        dialysisManagerMapper.delete(new LambdaQueryWrapper<DialysisManagerDO>().eq(DialysisManagerDO::getPatientId, createReqVO.getPatientId()));
        TeamLogEvent teamLogEvent = new TeamLogEvent("");
        TeamLogDO teamLogDO = new TeamLogDO();
        teamLogDO.setAdjustTime(DateUtil.date());
        teamLogDO.setContent("删除");
        teamLogDO.setPatientId(createReqVO.getPatientId());
        teamLogDO.setPatientName(createReqVO.getPatientName());
        teamLogDO.setDialyzeName(createReqVO.getDialysisName());
        teamLogDO.setFacilityName(createReqVO.getFacilityName());
        teamLogDO.setDeptId(createReqVO.getDeptId());
        Week week = DateUtil.dayOfWeekEnum(createReqVO.getClassesTime());
        String weekStr = week.toChinese("周");
        StringBuilder sb = new StringBuilder();
        sb.append(weekStr).append(createReqVO.getClassesTime() != null ? DateUtil.format(createReqVO.getClassesTime(), DatePattern.NORM_DATE_PATTERN) : "");
        teamLogDO.setTeamTime(sb.toString());
        DictDataRespDTO arrangeClassesPeriodTime = dictDataApi.getDictData("arrangeClassesPeriodTime", createReqVO.getDayState());
        teamLogDO.setWeekFlag(arrangeClassesPeriodTime != null ? arrangeClassesPeriodTime.getLabel() : null);
        teamLogEvent.setTeamLogDO(teamLogDO);
        applicationContext.publishEvent(teamLogEvent);
    }


    @Override
    public ArrangeClassesDO getArrangeClasses(Long id) {
        return arrangeClassesMapper.selectById(id);
    }

    @Override
    public List<ArrangeClassesDO> getArrangeClassesList(Collection<Long> ids) {
        return arrangeClassesMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ArrangeClassesDO> getArrangeClassesPage(ArrangeClassesPageReqVO pageReqVO) {
        return arrangeClassesMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ArrangeClassesDO> getArrangeClassesList(ArrangeClassesCreateReqVO createReqVO) {
        return arrangeClassesMapper.selectList(createReqVO);
    }

    @Override
    public List<ArrangeClassesRespVO> tableHeadList(ArrangeClassesCreateReqVO createReqVO) {
        DateRange range = getHeadList(createReqVO);
        List<ArrangeClassesRespVO> headList = Lists.newArrayList();
        for (DateTime dateTime : range) {
            ArrangeClassesRespVO classesRespVO = new ArrangeClassesRespVO();
            int i = DateUtil.dayOfWeek(dateTime) - 1 == 0 ? 7 : DateUtil.dayOfWeek(dateTime) - 1;
            Week week = DateUtil.dayOfWeekEnum(dateTime);
            String weekStr = week.toChinese("周");
            classesRespVO.setClassesTime(dateTime);
            classesRespVO.setWeekState(i);
            classesRespVO.setWeekName(weekStr);
            headList.add(classesRespVO);
        }
        return headList;
    }

    private DateRange getHeadList(ArrangeClassesBaseVO classesBaseVO) {
        if (StringUtils.isEmpty(classesBaseVO.getClassesTime())) {
            classesBaseVO.setClassesTime(DateUtil.date());
        }
        Date dateWeek = null;
        if (0 == classesBaseVO.getTempType()) {
            if (classesBaseVO.getClassesTime() == null) {
                dateWeek = DateUtil.beginOfWeek(DateUtil.date());
            } else {
                dateWeek = DateUtil.beginOfWeek(classesBaseVO.getClassesTime());
            }
        }  else {
            //Date afterDayDate = DateUtils.getAfterDayWeek(DateUtil.endOfWeek(classesBaseVO.getClassesTime()), classesBaseVO.getTempType() - 1);
            dateWeek = DateUtil.beginOfWeek(classesBaseVO.getClassesTime());
        }
        DateRange range = DateUtil.range(DateUtil.beginOfWeek(dateWeek), DateUtil.endOfWeek(dateWeek), DateField.DAY_OF_WEEK);
        return range;
    }

    @Override
    public Map<Long, List<Map<String, Object>>> tableContentList(ArrangeClassesCreateReqVO createReqVO) {
        List<String> rangeList = Lists.newArrayList();
        if (createReqVO.getDayTime() != null) {
            createReqVO.setClassesTime(createReqVO.getDayTime());
        } else if (createReqVO.getClassesTime() == null) {
            createReqVO.setClassesTime(new Date());
        }
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getTempType, createReqVO.getTempType())
                .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getDayTime())
                .between(0 == createReqVO.getTempType(), ArrangeClassesDO::getClassesTime, DateUtil.beginOfWeek(createReqVO.getClassesTime()), DateUtil.endOfWeek(createReqVO.getClassesTime()))
        );
        List<ArrangeClassesRespVO> arrangeClassesRespVOS = ArrangeClassesConvert.INSTANCE.convertList(arrangeClassesDOS);
        List<FacilityDO> facilityDOS = facilityMapper.selectList().stream().sorted(Comparator.comparing(FacilityDO::getSort)).collect(Collectors.toList());
        List<FacilityRespVO> facilityRespVOS = FacilityConvert.INSTANCE.convertList(facilityDOS);
        List<DictDataRespDTO> arrangeClassesPeriodTime = dictDataApi.getDictListData("arrangeClassesPeriodTime");
        DateRange range = getHeadList(createReqVO);
        for (DateTime dateTime : range) {
            int i = DateUtil.dayOfWeek(dateTime) - 1 == 0 ? 7 : DateUtil.dayOfWeek(dateTime) - 1;
            rangeList.add(i + "");
        }
        if (CollectionUtil.isNotEmpty(facilityRespVOS)) {
            // 获取分区的排序
            FacilitySubareaPageReqVO facilitySubareaPageReqVO = new FacilitySubareaPageReqVO();
            facilitySubareaPageReqVO.setPageSize(9999);
            Map<Long, Integer> facilitySubareaSortMap = facilitySubareaMapper.selectPage(facilitySubareaPageReqVO).getList()
                    .stream()
                    .collect(Collectors.toMap(FacilitySubareaDO::getId, FacilitySubareaDO::getSort));

            // 获取分区对应的机号排序
            FacilityPageReqVO facilityPageReqVO = new FacilityPageReqVO();
            facilityPageReqVO.setPageSize(9999);
            Map<Long, Map<String, Integer>> facilityMap = facilityMapper.selectPage(facilityPageReqVO).getList()
                    .stream()
                    .collect(Collectors.groupingBy(
                            FacilityDO::getSubareaId,
                            Collectors.toMap(
                                    FacilityDO::getCode,
                                    FacilityDO::getSort
                            )
                    ));


            List<Map<String, Object>> skeletonList = facilityRespVOS.stream().map(facilityRespVO -> {
                Map<String, Object> map = Maps.newHashMap();
                map.put("facilityId", facilityRespVO.getId());
                map.put("facilityName", facilityRespVO.getCode());
                map.put("facilitySubareaId", facilityRespVO.getSubareaId());
                map.put("faciitySubareaName", facilityRespVO.getSubareaName());
                // 分区排序
                map.put("facilitySubareaSort", facilitySubareaSortMap.getOrDefault(facilityRespVO.getSubareaId(), 9999));
                // 机号排序
                map.put("facilitySort", facilityMap.get(facilityRespVO.getSubareaId()).getOrDefault(facilityRespVO.getCode(), 9999));

                for (String s : rangeList) {
                    if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
                        for (DictDataRespDTO dictDataRespDTO : arrangeClassesPeriodTime) {
                            map.put(s + dictDataRespDTO.getValue(), "&nbsp;");
                        }
                    }
                }
                FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, facilityRespVO.getId());
                if (facilityManagerDO != null) {
                    FacilityNameDO facilityNameDO = facilityNameMapper.selectById(facilityManagerDO.getFacilityNameId());
                    if (facilityNameDO != null && 1 == facilityNameDO.getState()) {
                        map.put("hemofilter", 1);
                    } else {
                        map.put("hemofilter", 0);
                    }
                    FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(facilityRespVO.getSubareaId());
                    if (facilitySubareaDO != null && 1 == facilitySubareaDO.getState()) {
                        map.put("infectious", 1);
                    } else {
                        map.put("infectious", 0);
                    }
                }
                return map;
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(arrangeClassesRespVOS)) {
                if (CollectionUtil.isNotEmpty(facilityDOS)) {
                    arrangeClassesRespVOS.forEach(arrangeClassesRespVO -> {
                        facilityDOS.forEach(facilityDO -> {
                            if (arrangeClassesRespVO.getFacilityId().equals(facilityDO.getId())) {
                                arrangeClassesRespVO.setFacilitySubareaId(facilityDO.getSubareaId());
                                arrangeClassesRespVO.setFaciitySubareaName(facilityDO.getSubareaName());
                            }
                        });
                    });
                }
                MPJLambdaWrapper<DialysisProtocolDO> wrapper = new MPJLambdaWrapper<>(DialysisProtocolDO.class);
                wrapper.leftJoin(DialyzeOptionDO.class, DialyzeOptionDO::getId, DialysisProtocolDO::getPatientDialyzeId)
                        .select(DialysisProtocolDO::getHemodialysisDevice, DialysisProtocolDO::getBloodFilter, DialysisProtocolDO::getPerfume,DialysisProtocolDO::getPatientId,DialysisProtocolDO::getDialyzeId)
                        .eq(DialysisProtocolDO::getProtocolType, 1)
                        .eq(DialyzeOptionDO::getPid,0L);
                List<DialysisProtocolDO> dialysisProtocolDOS = dialysisProtocolMapper.selectList(wrapper);

                List<HisConsumablesDO> hisConsumablesList = hisConsumablesMapper.selectList();
                List<Map<String, Object>> skeletonListMap = skeletonList.stream().peek(map -> {
                    if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
                        for (String s : rangeList) {
                            if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
                                for (DictDataRespDTO dictDataRespDTO : arrangeClassesPeriodTime) {
                                    List<ArrangeClassesRespVO> arrangeClassesRespVOList = arrangeClassesRespVOS.stream().filter(arrangeClassesRespVO ->
                                            arrangeClassesRespVO.getDayState().equals(dictDataRespDTO.getValue())
                                                    && arrangeClassesRespVO.getWeekState() == (Integer.valueOf(s))
                                                    && arrangeClassesRespVO.getFacilityId().equals(map.get("facilityId"))
                                                    && arrangeClassesRespVO.getFacilitySubareaId().equals(map.get("facilitySubareaId"))).collect(Collectors.toList());
                                    List<ArrangeClassesRespVO> classesRespVOS = arrangeClassesRespVOList.stream().filter(arrangeClassesRespVO -> arrangeClassesRespVO.getDayState().equals(dictDataRespDTO.getValue())).collect(Collectors.toList());
                                    if (CollectionUtil.isNotEmpty(classesRespVOS)) {
                                        //患者透析方案的透析器，血滤器，灌流器型号
                                        ArrangeClassesRespVO arrangeClassesRespVO = arrangeClassesRespVOList.stream().findFirst().get();
                                        DialysisProtocolDO dialysisProtocolDO = null;
                                        if (arrangeClassesRespVO != null) {
                                            if (!StringUtils.isEmpty(arrangeClassesRespVO.getProtocolId())) {
                                                dialysisProtocolDO = dialysisProtocolMapper.selectById(arrangeClassesRespVO.getProtocolId());
                                            }else {
                                                List<DialysisProtocolDO> collect = dialysisProtocolDOS.stream().filter(dialysisProtocolDO1 ->
                                                        arrangeClassesRespVO.getPatientId().equals(dialysisProtocolDO1.getPatientId()) && arrangeClassesRespVO.getDialysisValue().equals(String.valueOf(dialysisProtocolDO1.getDialyzeId()))
                                                ).collect(Collectors.toList());
                                                if (!CollectionUtils.isEmpty(collect)) {
                                                    dialysisProtocolDO = collect.get(0);
                                                }
                                            }
                                            if (dialysisProtocolDO != null) {
                                                StringBuilder sb = new StringBuilder();
                                                if (StrUtil.isNotEmpty(dialysisProtocolDO.getHemodialysisDevice())) {
                                                    sb.append(dialysisProtocolDO.getHemodialysisDevice()).append(",");
                                                }
                                                if (StrUtil.isNotEmpty(dialysisProtocolDO.getBloodFilter())) {
                                                    sb.append(dialysisProtocolDO.getBloodFilter()).append(",");
                                                }
                                                if (StrUtil.isNotEmpty(dialysisProtocolDO.getPerfume())) {
                                                    sb.append(dialysisProtocolDO.getPerfume()).append(",");
                                                }
                                                String str = sb.toString().replaceAll(",+$", "");
                                                if (StrUtil.isNotEmpty(str)) {
                                                    List<String> collect = Arrays.stream(str.split(",")).collect(Collectors.toList());
                                                    List<HisConsumablesDO> hisConsumablesDOS = hisConsumablesList.stream().filter(hisConsumablesDO -> collect.contains(String.valueOf(hisConsumablesDO.getConsumId()))).collect(Collectors.toList());
                                                    if (CollectionUtil.isNotEmpty(hisConsumablesDOS)) {
                                                        String consumSpec = hisConsumablesDOS.stream().map(HisConsumablesDO::getConsumSpec).collect(Collectors.joining(","));
                                                        arrangeClassesRespVO.setConsumSpec(consumSpec);
                                                    }
                                                }
                                            }
                                        }
                                        map.put(s + dictDataRespDTO.getValue(), arrangeClassesRespVO);
                                    }
                                }
                            }
                        }
                    }
                }).collect(Collectors.toList());
                Map<Long, List<Map<String, Object>>> collect1 = skeletonListMap.stream().collect(Collectors.groupingBy(map -> (Long) map.get("facilitySubareaId")));
                // 为每个分区的 机号进行排序
                collect1.forEach((k, v) -> {
                    v = v.stream().sorted(Comparator.comparingInt(a -> (Integer) a.get("facilitySort"))).collect(Collectors.toList());
                });
                return collect1;
            }
            // 为每个分区的 机号进行排序
            Map<Long, List<Map<String, Object>>> collect = skeletonList.stream().collect(Collectors.groupingBy(map -> (Long) map.get("facilitySubareaId")));
            collect.forEach((k, v) -> {
                v = v.stream().sorted(Comparator.comparingInt(a -> (Integer) a.get("facilitySort"))).collect(Collectors.toList());
            });
            return collect;
        }
        return null;
    }

    @Override
    public List<String> checkPrompt(ArrangeClassesCreateReqVO createReqVO) {
        List<String> list = com.google.common.collect.Lists.newArrayList();
        PatientDO patientDO = patientMapper.selectOne(PatientDO::getId, createReqVO.getPatientId());
        if (patientDO != null) {
            //分区
            FacilitySubareaDO facilitySubareaDO = subareaMapper.selectById(createReqVO.getFacilitySubareaId());
            //患者有传染病和分区没有传染病
            if (StrUtil.isNotEmpty(patientDO.getInfect()) && "0".equals(facilitySubareaDO.getType())) {
                List<DictDataRespDTO> dictDataListByBatchValue = dictDataApi.getDictDataListByBatchValue(Arrays.stream(patientDO.getInfect().split(",")).collect(Collectors.toList()), "infect");
                list.add("患者有传染病(" + dictDataListByBatchValue.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(",")) + ")与该透析机不匹配，是否在此排班?");
            }
            //患者没有传染病和分区有传染病
            if (StrUtil.isEmpty(patientDO.getInfect()) && !facilitySubareaDO.getType().contains("0")) {
                list.add("患者没有传染病与该透析机不匹配,是否在此排班?");
            }
            //检测机号是否和患者的透析模式匹配
            List<FacilityManagerDO> facilityManagerDOS = facilityManagerMapper.selectList(FacilityManagerDO::getFacilityId, createReqVO.getFacilityId());
            if (CollectionUtil.isNotEmpty(facilityManagerDOS)) {
                String healModes = facilityManagerDOS.stream().map(FacilityManagerDO::getHealMode).collect(Collectors.toSet()).stream().collect(Collectors.joining(","));
                if (!healModes.contains(createReqVO.getDialysisValue())) {
                    list.add("该透析模式与设备类型不匹配,确定排班?");
                }
            }
        }
        return list;
    }

    /**
     * 修改排班
     * @param createReqVO
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateArrangeClasses(ArrangeClassesCreateReqVO createReqVO, HttpServletRequest request){
        ArrangeClassesDO arrangeClasses = ArrangeClassesConvert.INSTANCE.convert(createReqVO);
        long loginIdAsLong = StpUtil.getLoginIdAsLong();
        if (loginIdAsLong != 1 && createReqVO.getTempType() == 0) {
            if (arrangeClasses.getClassesTime().before(DateUtil.beginOfDay(DateUtil.date()))) {
                throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK);
            }
        }
        PatientDO patientDO = patientMapper.selectById(arrangeClasses.getPatientId());
        if (patientDO != null) {
            arrangeClasses.setPatientName(patientDO.getName());
            arrangeClasses.setPatientSrouce(patientDO.getPatientSource());
            arrangeClasses.setInfects(patientDO.getInfect());
            arrangeClasses.setLabels(patientDO.getLabels());
        }
        DictDataRespDTO dictData = dictDataApi.getDictData("dialyze_way", createReqVO.getDialysisValue());
        arrangeClasses.setDialysisName(dictData == null ? null : dictData.getLabel());
        FacilityDO facilityDO = facilityMapper.selectById(createReqVO.getFacilityId());
        arrangeClasses.setFacilityName(facilityDO == null ? null : facilityDO.getCode());
        FacilitySubareaDO facilitySubareaDO = subareaMapper.selectById(createReqVO.getFacilitySubareaId());
        arrangeClasses.setFaciitySubareaName(facilitySubareaDO == null ? null : facilitySubareaDO.getName());

        int weekState = DateUtil.dayOfWeek(createReqVO.getClassesTime()) - 1 == 0 ? 7 : DateUtil.dayOfWeek(createReqVO.getClassesTime()) - 1;
        arrangeClasses.setWeekState(weekState);

        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:batchUpdateClasses:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(30L, TimeUnit.SECONDS))) {
                //1直接更新
                if("1".equals(createReqVO.getUpdateType())){
                    arrangeClassesMapper.updateById(arrangeClasses);
                }else if("2".equals(createReqVO.getUpdateType())){  //2对换排班
                    //患者的旧排班
                    ArrangeClassesDO oldArrangeClassesDO = arrangeClassesMapper.selectById(createReqVO.getId());
                    /*对换的患者排班*/
                    QueryWrapper<ArrangeClassesDO> tradeQueryWrapper = new QueryWrapper<>();
                    //修改了患者
                    if(!createReqVO.getPatientId().equals(oldArrangeClassesDO.getPatientId())){
                        tradeQueryWrapper.lambda().eq(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
                                .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId()).last("limit 1");
                    }else{
                        tradeQueryWrapper.lambda().eq(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
                                .eq(ArrangeClassesDO::getDayState, createReqVO.getDayState())
                                .eq(ArrangeClassesDO::getFacilityId, createReqVO.getFacilityId()).last("limit 1");
                    }
                    ArrangeClassesDO tradeArrangeClassesDO = arrangeClassesMapper.selectOne(tradeQueryWrapper);

                    //保存患者新排班
                    arrangeClassesMapper.updateById(arrangeClasses);

                    //对换的患者排班，对调排班的班次、患者、透析模式
                    if(!createReqVO.getPatientId().equals(oldArrangeClassesDO.getPatientId())) {
                        tradeArrangeClassesDO.setPatientId(oldArrangeClassesDO.getPatientId());
                        tradeArrangeClassesDO.setPatientName(oldArrangeClassesDO.getPatientName());
                        tradeArrangeClassesDO.setPatientSrouce(oldArrangeClassesDO.getPatientSrouce());
                        tradeArrangeClassesDO.setInfects(oldArrangeClassesDO.getInfects());
                        tradeArrangeClassesDO.setLabels(oldArrangeClassesDO.getLabels());
                    }else{
                        tradeArrangeClassesDO.setFacilityId(oldArrangeClassesDO.getFacilityId());
                        tradeArrangeClassesDO.setFacilityName(oldArrangeClassesDO.getFacilityName());
                        tradeArrangeClassesDO.setFacilitySubareaId(oldArrangeClassesDO.getFacilitySubareaId());
                        tradeArrangeClassesDO.setFaciitySubareaName(oldArrangeClassesDO.getFaciitySubareaName());
                        tradeArrangeClassesDO.setDayState(oldArrangeClassesDO.getDayState());
                    }
                    tradeArrangeClassesDO.setDialysisValue(oldArrangeClassesDO.getDialysisValue());
                    tradeArrangeClassesDO.setDialysisName(oldArrangeClassesDO.getDialysisName());
                    tradeArrangeClassesDO.setWeekState(oldArrangeClassesDO.getWeekState());
                    tradeArrangeClassesDO.setTempType(oldArrangeClassesDO.getTempType());
                    arrangeClassesMapper.updateById(tradeArrangeClassesDO);

                }else{// 3替换排班
                    //删除替换的患者排班
                    QueryWrapper<ArrangeClassesDO> tradeQueryWrapper = new QueryWrapper<>();
                    tradeQueryWrapper.lambda().eq(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
                            .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId());
                    arrangeClassesMapper.delete(tradeQueryWrapper);

                    //保存患者新排班
                    arrangeClassesMapper.updateById(arrangeClasses);

                }
            }
        }catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("新增排班信息异常:{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }



    //0直接排班  1替换/对换  2一个患者只能当天排班一次 3 历史排班不可修改
    @Override
    public Integer checkUpdateStatus(ArrangeClassesCreateReqVO createReqVO){
        long diff0 = diffCurrentDate(createReqVO.getClassesTime(), createReqVO.getDayState());
        if (diff0 <= 0) {
            throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK);
        }
        ArrangeClassesDO oldArrangeClassesDO = arrangeClassesMapper.selectById(createReqVO.getId());
        //如果是更新患者时，查询该患者是否存在排班   存在排班时，可以进行替换或者对换
        QueryWrapper<ArrangeClassesDO> acQueryWrapper = new QueryWrapper<>();
        if(createReqVO.getId() != null && !oldArrangeClassesDO.getPatientId().equals(createReqVO.getPatientId())){
            acQueryWrapper.lambda().eq(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
                    .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                    .ne(ArrangeClassesDO::getId, createReqVO.getId());
            ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(acQueryWrapper);
            if(arrangeClassesDO != null) {
                long diff = diffCurrentDate(arrangeClassesDO.getClassesTime(), arrangeClassesDO.getDayState());
                if (diff > 0) {
                    return 1;
                } else {
                    //return 2;
                    throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK);
                }
            }
        }else{
            acQueryWrapper.lambda().eq(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
                    .eq(ArrangeClassesDO::getFacilityId, createReqVO.getFacilityId())
                    .eq(ArrangeClassesDO::getDayState, createReqVO.getDayState())
                    .ne(ArrangeClassesDO::getId, createReqVO.getId());
            ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(acQueryWrapper);
            if(arrangeClassesDO != null) {
                long diff = diffCurrentDate(arrangeClassesDO.getClassesTime(), arrangeClassesDO.getDayState());
                if (diff > 0) {
                    return 1;
                } else {
                    //return 3;
                    throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK);
                }
            }
        }
        return 0;
    }

    private long diffCurrentDate(Date classesTime, String dayState) {
        String timeToDay = DateUtils.getTimeToDay(classesTime);
        String cdate = "";
        if("a".equals(dayState)){
            cdate = timeToDay + " 12:00:00";
        }else if("b".equals(dayState)){
            cdate = timeToDay + " 18:00:00";
        }else{
            cdate = timeToDay + " 23:59:59";
        }
        Date date = DateUtils.parseDate(cdate, DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        return DateUtils.diff(date, new Date());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateClasses(ArrangeClassesUpdateReqVO updateReqVO, HttpServletRequest request) {
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:batchUpdateClasses:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                List<Integer> dayOfWeekList = Lists.newArrayList();
                List<Date> weekTime = Lists.newArrayList();
                DateRange range = DateUtil.range(updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime()), DateField.DAY_OF_WEEK);
                for (DateTime dateTime : range) {
                    int i = DateUtil.dayOfWeek(dateTime) - 1 == 0 ? 7 : DateUtil.dayOfWeek(dateTime) - 1;
                    dayOfWeekList.add(i);
                    weekTime.add(dateTime);
                }

                DateTime currentWeek = DateUtil.beginOfWeek(DateUtil.date());
                int compare = DateUtil.compare(updateReqVO.getClassesTime(), currentWeek);
                if (0 == compare) {
                    int i = DateUtil.dayOfWeek(DateUtil.date());
                    List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                            .eqIfPresent(ArrangeClassesDO::getTempType, updateReqVO.getTempType())
                            .betweenIfPresent(ArrangeClassesDO::getWeekState, i, 7));
                    if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
                        for (int ii = i;ii<8;ii++) {
                            Date classesTime = updateReqVO.getClassesTime();
                            Calendar instance = Calendar.getInstance();
                            instance.setTime(classesTime);
                            instance.add(Calendar.DAY_OF_MONTH,ii -1);
                            Date time = instance.getTime();
                            arrangeClassesMapper.deleteByClassTime(time);
                        }


                        for (ArrangeClassesDO arrangeClassesDO : arrangeClassesDOS) {
                            // 设置时间
                            Date classesTime = updateReqVO.getClassesTime();
                            Integer weekState = arrangeClassesDO.getWeekState();
                            Calendar instance = Calendar.getInstance();
                            instance.setTime(classesTime);
                            instance.add(Calendar.DAY_OF_MONTH,weekState -1);
                            Date time = instance.getTime();
                            arrangeClassesDO.setClassesTime(time);
                            //arrangeClassesMapper.deleteArrange(arrangeClassesDO);
                            arrangeClassesDO.setTempType(0);
                            arrangeClassesDO.setId(null);
                            arrangeClassesMapper.insert(arrangeClassesDO);

                           /* ArrangeClassesDO classesDO = getOldClasses(updateReqVO, arrangeClassesDO);
                            if (classesDO == null) {
                                ArrangeClassesDO classesDO1 = getOldClassesByPatientId(updateReqVO, arrangeClassesDO);
                                if (classesDO1 != null) {
                                    continue;
                                }
                                arrangeClassesDO.setId(null);
                                arrangeClassesDO.setTempType(0);
                                for (Integer week : dayOfWeekList) {
                                    if (arrangeClassesDO.getWeekState() == week) {
                                        arrangeClassesDO.setClassesTime(weekTime.get(week - 1));
                                        break;
                                    }
                                }
                                arrangeClassesMapper.insert(arrangeClassesDO);
                            } else {
                                ArrangeClassesDO classesDO1 = getOldClassesByPatientId(updateReqVO, arrangeClassesDO);
                                if (classesDO1 != null) {
                                    continue;
                                }
                                arrangeClassesDO.setId(null);
                                arrangeClassesDO.setTempType(0);
                                for (Integer week : dayOfWeekList) {
                                    if (arrangeClassesDO.getWeekState() == week) {
                                        arrangeClassesDO.setClassesTime(weekTime.get(week - 1));
                                        break;
                                    }
                                }
                                updateClasses(updateReqVO, arrangeClassesDO);
                            }*/
                        }
                    }
                } else if (updateReqVO.getClassesTime().after(DateUtil.beginOfWeek(DateUtil.date()))) {
                    List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                            .eqIfPresent(ArrangeClassesDO::getTempType, updateReqVO.getTempType())
                            .betweenIfPresent(ArrangeClassesDO::getWeekState, 1, 7));
                    if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
                        List<ArrangeClassesDO> arrangeClassesDOS1 = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                                .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                                .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime())));
                        if (CollectionUtil.isNotEmpty(arrangeClassesDOS1)) {
                            arrangeClassesMapper.delete(new LambdaQueryWrapper<ArrangeClassesDO>()
                                    .eq(ArrangeClassesDO::getTempType, 0)
                                    .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime())));
                        }
                        List<ArrangeClassesDO> collect = arrangeClassesDOS.stream().peek(arrangeClassesDO -> {
                            arrangeClassesDO.setId(null);
                            arrangeClassesDO.setTempType(0);
                            for (Integer week : dayOfWeekList) {
                                if (arrangeClassesDO.getWeekState() == week) {
                                    arrangeClassesDO.setClassesTime(weekTime.get(week - 1));
                                }
                            }
                        }).collect(Collectors.toList());
                        arrangeClassesMapper.insertBatch(collect);

                    }
                }
            } else {
                return;
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("替换排班异常信息:{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }


    }

    @Override
    public void importMBClasses(ArrangeClassesUpdateReqVO updateReqVO, HttpServletRequest request) {
        if (updateReqVO.getTempType() == null) throw new ServiceException(403, "请选择导入到哪个模板中");
        if (updateReqVO.getClassesSource() == null) throw new ServiceException(403, "请选择导入的模板");
        if (updateReqVO.getClassesSource() == 0 && updateReqVO.getClassesTime() == null) throw new ServiceException(403, "请选择导入模板的周次");
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:importMBClasses:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                List<Integer> dayOfWeekList = Lists.newArrayList();
                List<Date> weekTime = Lists.newArrayList();
                DateRange range = DateUtil.range(updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime()), DateField.DAY_OF_WEEK);
                for (DateTime dateTime : range) {
                    int i = DateUtil.dayOfWeek(dateTime) - 1 == 0 ? 7 : DateUtil.dayOfWeek(dateTime) - 1;
                    dayOfWeekList.add(i);
                    weekTime.add(dateTime);
                }

                // 查询导入源模板的排班信息（一周的信息）
                List<ArrangeClassesDO> arrangeClassesSource = CollUtil.newArrayList();
                if (updateReqVO.getClassesSource() == 0) { // 预约排班
                    arrangeClassesSource = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                            .eqIfPresent(ArrangeClassesDO::getTempType, updateReqVO.getClassesSource())
                            .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime()))
                    );
                } else { // 模版排班
                    arrangeClassesSource = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                            .eqIfPresent(ArrangeClassesDO::getTempType, updateReqVO.getClassesSource())
                            .betweenIfPresent(ArrangeClassesDO::getWeekState, 1, 7)
                    );
                }
                if (CollectionUtil.isNotEmpty(arrangeClassesSource)) {
                    // 查询当前模板的排班信息（一周的信息）
                    List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                            .eqIfPresent(ArrangeClassesDO::getTempType, updateReqVO.getTempType())
                            .betweenIfPresent(ArrangeClassesDO::getWeekState, 1, 7)
                    );
                    if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
                        // 删除当前模板的排班信息（一周的信息）
                        arrangeClassesMapper.delete(new LambdaQueryWrapperX<ArrangeClassesDO>()
                                .eq(ArrangeClassesDO::getTempType, updateReqVO.getTempType())
                                .betweenIfPresent(ArrangeClassesDO::getWeekState, 1, 7)
                        );
                    }
                    // 处理导入源模板的排班信息
                    List<ArrangeClassesDO> collect = arrangeClassesSource.stream().peek(arrangeClassesDO -> {
                        arrangeClassesDO.setId(null);
                        arrangeClassesDO.setTempType(updateReqVO.getTempType());
                        for (Integer week : dayOfWeekList) {
                            if (arrangeClassesDO.getWeekState() == week) {
                                arrangeClassesDO.setClassesTime(weekTime.get(week - 1));
                            }
                        }
                    }).collect(Collectors.toList());
                    arrangeClassesMapper.insertBatch(collect);

                }
            } else {
                return;
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("替换排班异常信息:{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    private void updateClasses(ArrangeClassesUpdateReqVO updateReqVO, ArrangeClassesDO arrangeClassesDO) {
        arrangeClassesMapper.update(arrangeClassesDO, new LambdaUpdateWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getWeekState, arrangeClassesDO.getWeekState())
                .eq(ArrangeClassesDO::getDayState, arrangeClassesDO.getDayState())
                .eq(ArrangeClassesDO::getFacilityId, arrangeClassesDO.getFacilityId())
                .eq(ArrangeClassesDO::getFacilitySubareaId, arrangeClassesDO.getFacilitySubareaId())
                .eq(ArrangeClassesDO::getTempType, 0)
                .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime())));
    }

    private ArrangeClassesDO getOldClassesByPatientId(ArrangeClassesUpdateReqVO updateReqVO, ArrangeClassesDO arrangeClassesDO) {
        ArrangeClassesDO classesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getWeekState, arrangeClassesDO.getWeekState())
                .eqIfPresent(ArrangeClassesDO::getPatientId, arrangeClassesDO.getPatientId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime())));
        return classesDO;
    }

    private ArrangeClassesDO getOldClasses(ArrangeClassesUpdateReqVO updateReqVO, ArrangeClassesDO arrangeClassesDO) {
        ArrangeClassesDO classesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getWeekState, arrangeClassesDO.getWeekState())
                .eqIfPresent(ArrangeClassesDO::getDayState, arrangeClassesDO.getDayState())
                .eqIfPresent(ArrangeClassesDO::getFacilityId, arrangeClassesDO.getFacilityId())
                .eqIfPresent(ArrangeClassesDO::getFacilitySubareaId, arrangeClassesDO.getFacilitySubareaId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime())));
        return classesDO;
    }

    @Override
    public Map<String, Object> getCalendars(ArrangeClassesCreateReqVO createReqVO) {
        PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
        Map<String, Object> map = Maps.newHashMap();
        List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPatientId, createReqVO.getPatientId());
        if (CollectionUtil.isNotEmpty(dialyzeOptionDOS)) {
            String dialyze = dialyzeOptionDOS.stream().map(dialyzeOptionDO -> {
                DictDataRespDTO pinlv = dictDataApi.getDictData("zhouqi", dialyzeOptionDO.getFrequencyDictValue());
                DictDataRespDTO cycleDay = dictDataApi.getDictData("cycle_day", dialyzeOptionDO.getCycleDictValue());
                DictDataRespDTO cishu = dictDataApi.getDictData("cishu", dialyzeOptionDO.getNumberDictValue());
                DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", dialyzeOptionDO.getDialyzeDictValue());
                StringBuilder sb = new StringBuilder();
                if (StrUtil.isNotEmpty(dialyzeOptionDO.getCycleDictValue())) {
                    sb.append(dialyzeWay == null ? "" : dialyzeWay.getLabel())
                            .append("(")
                            .append(pinlv == null ? "" : pinlv.getLabel())
                            .append(cycleDay == null ? "" : cycleDay.getLabel())
                            .append(")");
                }
                if (StrUtil.isNotEmpty(dialyzeOptionDO.getNumberDictValue())) {
                    sb.append(dialyzeWay == null ? "" : dialyzeWay.getLabel())
                            .append("(")
                            .append(pinlv == null ? "" : pinlv.getLabel())
                            .append(cishu == null ? "" : cishu.getLabel())
                            .append(")");
                }
                return sb.toString();
            }).collect(Collectors.joining("、"));
            map.put("dialyze", dialyze);
        } else {
            map.put("dialyze", null);
        }
        Date beginDate = null;
        Date endDate = null;
        if (createReqVO.getMonth() == null) {
            beginDate = DateUtil.beginOfMonth(DateUtil.date());
            endDate = DateUtil.endOfMonth(DateUtil.date());
        } else {
            DateTime parse = DateUtil.parse(createReqVO.getMonth(), DatePattern.NORM_MONTH_PATTERN);
            beginDate = DateUtil.beginOfMonth(parse);
            endDate = DateUtil.endOfMonth(parse);
        }
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eq(ArrangeClassesDO::getTempType, "0")
                .between(ArrangeClassesDO::getClassesTime, beginDate, endDate));
        List<ArrangeClassesRespVO> arrangeClassesRespVOList = ArrangeClassesConvert.INSTANCE.convertList(arrangeClassesDOS);
        if (CollectionUtil.isNotEmpty(arrangeClassesRespVOList)) {
            arrangeClassesRespVOList = arrangeClassesRespVOList.stream().peek(arrangeClassesRespVO -> {
                if (patientDO != null) {
                    DictDataRespDTO patientSource = dictDataApi.getDictData("patient_source", patientDO.getPatientSource());
                    arrangeClassesRespVO.setPatientSrouce(patientSource.getLabel());
                }
            }).collect(Collectors.toList());
        }
        map.put("teamPatientDOS", arrangeClassesRespVOList);
        return map;
    }

    @Override
    public List<ArrangeClassesRespVO> patientList(ArrangeClassesCreateReqVO createVO) {

        if(!com.thj.boot.common.utils.StringUtils.isEmpty(createVO.getMore())) {
            String more = createVO.getMore();
            String[] split = more.split("");
            String join = String.join("%", split);
            createVO.setMore(join);

        }
        MPJLambdaWrapper<ArrangeClassesDO> wrapper = new MPJLambdaWrapper<>(ArrangeClassesDO.class);
        wrapper.leftJoin(PatientDO.class, PatientDO::getId, ArrangeClassesDO::getPatientId)
                .selectAll(ArrangeClassesDO.class)
                .select(PatientDO::getDialyzeNo, PatientDO::getName, PatientDO::getSex, PatientDO::getAge, PatientDO::getEndemicArea
                        , PatientDO::getBedNo, PatientDO::getInitDialyzeNo, PatientDO::getFirstReceiveTime, PatientDO::getEnterWay, PatientDO::getSpellName)
                .eq(ArrangeClassesDO::getClassesTime, createVO.getClassesTime())
                .eq(ArrangeClassesDO::getTempType, 0)
                .eq(createVO.getDayState() != null && !createVO.getDayState().equals("d"), ArrangeClassesDO::getDayState, createVO.getDayState())
                .in(CollectionUtil.isNotEmpty(createVO.getFacilitySubareaIds()), ArrangeClassesDO::getFacilitySubareaId, createVO.getFacilitySubareaIds())
                .and(StrUtil.isNotEmpty(createVO.getMore()), i -> i
                        .like(PatientDO::getName, createVO.getMore())
                        .or()
                        .like(PatientDO::getSpellName, createVO.getMore())
                        .or()
                        .like(PatientDO::getDialyzeNo, createVO.getMore())).orderByAsc(ArrangeClassesDO::getFacilityId);
        List<ArrangeClassesRespVO> arrangeClassesRespVOList = arrangeClassesMapper.selectJoinList(ArrangeClassesRespVO.class, wrapper);
        if (CollectionUtil.isNotEmpty(arrangeClassesRespVOList)) {
            Stream<ArrangeClassesRespVO> arrangeClassesRespVOStream = arrangeClassesRespVOList.stream().peek(arrangeClassesRespVO -> {
                List<DryWeightDO> dryWeightDOS = dryWeightMapper.selectList(new LambdaQueryWrapperX<DryWeightDO>()
                        .eqIfPresent(DryWeightDO::getPatientId, arrangeClassesRespVO.getPatientId())
                        .orderByDesc(DryWeightDO::getId));
                if (CollectionUtil.isNotEmpty(dryWeightDOS)) {
                    DryWeightDO dryWeightDO = dryWeightDOS.stream().findFirst().get();
                    arrangeClassesRespVO.setDryWeight(StrUtil.isNotEmpty(dryWeightDO.getDryWeight()) ? dryWeightDO.getDryWeight() : null);
                }
                // 查询透析状态
                List<HemodialysisManagerDO> hemodialysisManagerDOS = hemodialysisManagerMapper.selectList(HemodialysisManagerDO::getPatientId, arrangeClassesRespVO.getPatientId(), HemodialysisManagerDO::getHemodialysisTime, arrangeClassesRespVO.getClassesTime());
                if (CollectionUtils.isEmpty(hemodialysisManagerDOS)) {
                    arrangeClassesRespVO.setCheckStatus(0);
                } else {
                    HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerDOS.get(0);
                    if (StringUtils.isEmpty(hemodialysisManagerDO.getPrescriptionState()) || 0 == hemodialysisManagerDO.getPrescriptionState()) {
                        arrangeClassesRespVO.setCheckStatus(0);
                    } else {
                        arrangeClassesRespVO.setCheckStatus(1);
                    }

                    if (!StringUtils.isEmpty(hemodialysisManagerDO.getFacilityId())) {
                        arrangeClassesRespVO.setFacilityId(hemodialysisManagerDO.getFacilityId());
                        if (!StringUtils.isEmpty(arrangeClassesRespVO.getFacilityId())) {
                            FacilityDO facilityDO = facilityMapper.selectOne(FacilityDO::getId, arrangeClassesRespVO.getFacilityId());
                            if (!StringUtils.isEmpty(facilityDO)) {
                                arrangeClassesRespVO.setSort(facilityDO.getSort());
                                arrangeClassesRespVO.setFacilityName(facilityDO.getCode());
                            }
                        }
                    }
                }

                List<DiseaseReasonDO> diseaseReasonDOS = diseaseReasonMapper.selectList(DiseaseReasonDO::getPatientId, arrangeClassesRespVO.getPatientId(), DiseaseReasonDO::getSync, 1);
                if (CollectionUtil.isNotEmpty(diseaseReasonDOS)) {
                    String diseaseReasonNames = diseaseReasonDOS.stream().sorted(Comparator.comparing(DiseaseReasonDO::getSorted,Comparator.nullsLast(Integer::compareTo))).map(diseaseReasonDO -> {
                        if(StrUtil.isNotEmpty(diseaseReasonDO.getCustomName())) {
                            return diseaseReasonDO.getCustomName();
                        }else if(StrUtil.isNotEmpty(diseaseReasonDO.getParentThreeName())){
                            return diseaseReasonDO.getParentThreeName();
                        }else {
                            return diseaseReasonDO.getParentTwoName();
                        }
                    }).collect(Collectors.joining(","));
                    arrangeClassesRespVO.setDiseaseReasonNames(diseaseReasonNames);
                }

                // 查询 当前患者 的 透析处方
/*                HemodialysisManagerDO managerDO = hemodialysisManagerMapper.selectOne(
                        new LambdaQueryWrapperX<HemodialysisManagerDO>()
                                .eq(HemodialysisManagerDO::getPatientId, arrangeClassesRespVO.getPatientId())
                                .eq(HemodialysisManagerDO::getHemodialysisTime, arrangeClassesRespVO.getClassesTime())
                );
//                // 透析处方（若确认 则使用透析处方的机号）
                if (managerDO != null && managerDO.getPrescriptionState() != null && managerDO.getPrescriptionState() == 1) {
                    // 使用确认后的机号
                    arrangeClassesRespVO.setFacilityId(managerDO.getFacilityId());
                }
                FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, arrangeClassesRespVO.getFacilityId());
                arrangeClassesRespVO.setEquipmentId(facilityManagerDO != null ? facilityManagerDO.getFacilityNameId() : null);
                arrangeClassesRespVO.setEquipmentType(facilityManagerDO != null ? facilityManagerDO.getFacilityTypeId() : null);
                arrangeClassesRespVO.setFacilityName(facilityManagerDO != null ? facilityManagerDO.getFacilityCode() : null);
                arrangeClassesRespVO.setSort(0);
                if (!StringUtils.isEmpty(arrangeClassesRespVO.getFacilityId())) {
                    FacilityDO facilityDO = facilityMapper.selectOne(FacilityDO::getId, arrangeClassesRespVO.getFacilityId());
                    if (!StringUtils.isEmpty(facilityDO)) {
                        arrangeClassesRespVO.setSort(facilityDO.getSort());
                    }
                }*/

            });

            if (StrUtil.isNotBlank(createVO.getSortType())) {
                switch (createVO.getSortType()) {
                    // 机号排序
                    case "1":
                        Comparator<ArrangeClassesRespVO> comparator = (obj1, obj2) -> {
                            // 获取患者的机号
                            Integer facilityName1 = null;
                            Integer facilityName2 = null;
                            if (!StringUtils.isEmpty(obj1.getSort())) facilityName1 = obj1.getSort();
                            if (!StringUtils.isEmpty(obj2.getSort())) facilityName2 = obj2.getSort();

                            // 患者没有机号往后排放
                            if (facilityName1 == null && facilityName2 == null) return 0;
                            if (facilityName1 == null) return 1;
                            if (facilityName2 == null) return -1;
                            return facilityName1.compareTo(facilityName2);
                        };
                        // 对数据列表排序
                        arrangeClassesRespVOList = arrangeClassesRespVOStream.sorted(comparator).collect(Collectors.toList());
                        break;
                    // 签到时间排序
                    case "2":
                        Comparator<ArrangeClassesRespVO> comparator1 = (obj1, obj2) -> {
                            // 获取患者签到时间
                            Date RegisterTime1 = obj1.getRegisterTime();
                            Date RegisterTime2 = obj2.getRegisterTime();

                            // 没有签到的患者往后排放
                            if (RegisterTime1 == null && RegisterTime2 == null) return 0;
                            if (RegisterTime1 == null) return 1;
                            if (RegisterTime2 == null) return -1;
                            return RegisterTime1.compareTo(RegisterTime2);
                        };
                        // 已为默认 无需再写
                        // 对数据列表排序
                        arrangeClassesRespVOList = arrangeClassesRespVOStream.sorted(comparator1).collect(Collectors.toList());
                        break;
                }
            }




        }
        return arrangeClassesRespVOList;
    }

    @Override
    public Map<String, Object> dialyzBudgePage(ArrangeClassesCreateReqVO createVO) {
        Map<String, Object> map = Maps.newHashMap();
        List<String> list2 = Lists.newArrayList();
        Date beginDate = null;
        Date endDate = null;
        if (createVO.getStartTime() == null && createVO.getEndTime() == null) {
            beginDate = DateUtil.beginOfMonth(DateUtil.date());
            endDate = DateUtil.endOfMonth(DateUtil.date());
        } else {
            beginDate = createVO.getStartTime();
            endDate = createVO.getEndTime();
        }
        List<DialyzBudgetRespVO> budgetRespVOS = null;
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getTempType, 0)
                .between(ArrangeClassesDO::getClassesTime, beginDate, endDate)
                .like(StrUtil.isNotEmpty(createVO.getPatientName()), ArrangeClassesDO::getPatientName, createVO.getPatientName()));
        if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
            Set<Long> patientIds = arrangeClassesDOS.stream().map(ArrangeClassesDO::getPatientId).collect(Collectors.toSet());
            Date finalBeginDate = beginDate;
            Date finalEndDate = endDate;
            budgetRespVOS = patientIds.stream().map(patientId -> {
                List<String> list = com.google.common.collect.Lists.newArrayList();
                PatientDO patientDO = patientMapper.selectById(patientId);
                DialyzBudgetRespVO budgetRespVO = new DialyzBudgetRespVO();
                List<ArrangeClassesDO> arrangeClassesDOList = arrangeClassesMapper.selectList(new LambdaQueryWrapper<ArrangeClassesDO>()
                        .eq(ArrangeClassesDO::getTempType, 0)
                        .between(ArrangeClassesDO::getClassesTime, finalBeginDate, finalEndDate)
                        .eq(ArrangeClassesDO::getPatientId, patientId));
                String content = arrangeClassesDOList.stream().map(arrangeClassesDO -> {
                    return arrangeClassesDO.getDialysisName() + "【" + DateUtil.format(arrangeClassesDO.getClassesTime(), "MM-dd") + "】";
                }).collect(Collectors.joining("、"));
                if (patientDO != null) {
                    budgetRespVO.setPatientName(patientDO.getName());
                    budgetRespVO.setDialyzNo(patientDO.getDialyzeNo());
                }
                budgetRespVO.setContent(content);
                List<ArrangeClassesDO> patientDOList = arrangeClassesMapper.selectList(new LambdaQueryWrapper<ArrangeClassesDO>()
                        .eq(ArrangeClassesDO::getPatientId, patientId)
                        .eq(ArrangeClassesDO::getTempType, 0)
                        .between(ArrangeClassesDO::getClassesTime, finalBeginDate, finalEndDate)
                        .like(StrUtil.isNotEmpty(createVO.getPatientName()), ArrangeClassesDO::getPatientName, createVO.getPatientName()));
                if (CollectionUtil.isNotEmpty(patientDOList)) {
                    Set<String> dialyzeNameSet = patientDOList.stream().map(ArrangeClassesDO::getDialysisName).collect(Collectors.toSet());
                    for (String s : dialyzeNameSet) {
                        Long count = arrangeClassesMapper.selectCount(new LambdaQueryWrapper<ArrangeClassesDO>()
                                .eq(ArrangeClassesDO::getDialysisName, s)
                                .eq(ArrangeClassesDO::getPatientId, patientId)
                                .eq(ArrangeClassesDO::getTempType, 0)
                                .between(ArrangeClassesDO::getClassesTime, finalBeginDate, finalEndDate)
                                .like(StrUtil.isNotEmpty(createVO.getPatientName()), ArrangeClassesDO::getPatientName, createVO.getPatientName()));
                        list.add(s + "【" + count + "次】");
                    }
                    String collect = list.stream().collect(Collectors.joining("、"));
                    budgetRespVO.setTotal(collect);
                }
                return budgetRespVO;
            }).collect(Collectors.toList());
            Set<String> dialyzeNames = arrangeClassesDOS.stream().map(ArrangeClassesDO::getDialysisName).collect(Collectors.toSet());
            for (String dialyzeName : dialyzeNames) {
                Long count = arrangeClassesMapper.selectCount(new LambdaQueryWrapper<ArrangeClassesDO>()
                        .eq(ArrangeClassesDO::getDialysisName, dialyzeName)
                        .eq(ArrangeClassesDO::getTempType, 0)
                        .between(ArrangeClassesDO::getClassesTime, beginDate, endDate)
                        .like(StrUtil.isNotEmpty(createVO.getPatientName()), ArrangeClassesDO::getPatientName, createVO.getPatientName()));
                list2.add(dialyzeName + "【" + count + "次】");
            }
            String rowsTotal = list2.stream().collect(Collectors.joining("、"));
            map.put("rowsTotal", rowsTotal);
        }
        map.put("budgetRespVOS", budgetRespVOS);
        return map;
    }

    @Override
    public Map<String, Object> teamPatientInfo(Long patientId) {
        Map<String, Object> map = Maps.newHashMap();
        List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPatientId, patientId);
        if (CollectionUtil.isNotEmpty(dialyzeOptionDOS)) {
            String dialyze = dialyzeOptionDOS.stream().map(dialyzeOptionDO -> {
                DictDataRespDTO pinlv = dictDataApi.getDictData("zhouqi", dialyzeOptionDO.getFrequencyDictValue());
                DictDataRespDTO cycleDay = dictDataApi.getDictData("cycle_day", dialyzeOptionDO.getCycleDictValue());
                DictDataRespDTO cishu = dictDataApi.getDictData("cishu", dialyzeOptionDO.getNumberDictValue());
                DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", dialyzeOptionDO.getDialyzeDictValue());
                StringBuilder sb = new StringBuilder();
                if (StrUtil.isNotEmpty(dialyzeOptionDO.getCycleDictValue())) {
                    sb.append(dialyzeWay == null ? "" : dialyzeWay.getLabel())
                            .append("(")
                            .append(pinlv == null ? "" : pinlv.getLabel())
                            .append(cycleDay == null ? "" : cycleDay.getLabel())
                            .append(")");
                }
                if (StrUtil.isNotEmpty(dialyzeOptionDO.getNumberDictValue())) {
                    sb.append(dialyzeWay == null ? "" : dialyzeWay.getLabel())
                            .append("(")
                            .append(pinlv == null ? "" : pinlv.getLabel())
                            .append(cishu == null ? "" : cishu.getLabel())
                            .append(")");
                }
                return sb.toString();
            }).collect(Collectors.joining("、"));
            map.put("dialyze", dialyze);
        } else {
            map.put("dialyze", "");
        }
        UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
        createReqDTO.setId(StpUtil.getLoginIdAsLong());
        UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
        if (adminUser != null && !"0".equals(adminUser.getTempNumber())) {
            for (Integer i = 1; i <= Integer.valueOf(adminUser.getTempNumber()); i++) {
                List<DrainTeamAlreadyVO> teamAlreadyVOS = com.google.common.collect.Lists.newArrayList();
                List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getTempType, i)
                        .eqIfPresent(ArrangeClassesDO::getPatientId, patientId));
                teamAlreadyVOS = weekStateList(arrangeClassesDOS, teamAlreadyVOS);
                map.put("temp" + i, teamAlreadyVOS);
            }
        }
        return map;
    }

    @Override
    public List<ArrangeClassesRespVO> getMonthCalendars(ArrangeClassesCreateReqVO createReqVO) {
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfMonth(createReqVO.getDayTime()), DateUtil.endOfMonth(createReqVO.getDayTime())));
        return ArrangeClassesConvert.INSTANCE.convertList(arrangeClassesDOS);
    }

    @Override
    public Map<String, Object> tempPush(ArrangeClassesCreateReqVO createReqVO,HttpServletRequest request) {
        Map<String, Object> map = Maps.newHashMap();
        DateTime dateTime = DateUtil.offsetWeek(new Date(), 3);
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .notIn(ArrangeClassesDO::getTempType, 0));

        List<String> collect = Arrays.stream(createReqVO.getAdviceIds().split(",")).collect(Collectors.toList());
        // 长期医嘱
        DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectOne(DialysisAdviceDO::getId, collect.get(0));
        //
        String systemDeptId = request.getHeader("SystemDeptId");

        String cacheObject = RedisUtils.getCacheObject("tempNumber:" + systemDeptId);
        String weekCache = RedisUtils.getCacheObject("tempStartWeek:" + systemDeptId);

        int week = -1;
        if(!StringUtils.isEmpty(cacheObject) && !StringUtils.isEmpty(weekCache)) {
            String[] split = weekCache.split("-");
            LocalDate startTime = LocalDate.of(Integer.valueOf(split[0]), Integer.valueOf(split[1]), Integer.valueOf(split[2]));
            LocalDate now = LocalDate.now();
            int between = (int) ChronoUnit.WEEKS.between(startTime, now);
            week = between%Integer.valueOf(cacheObject);
        }
        DateTime tempDateTime = DateUtil.offsetWeek(new Date(),0);
        String tempDate = DateUtil.beginOfWeek(tempDateTime).toString(DatePattern.NORM_DATETIME_MS_PATTERN);
        Date afterDayDate = DateUtils.getAfterDayDate(DateUtil.beginOfWeek(new Date()), 0);;
        if (week > -1) {
            if (week == 0) {
                tempDate =  DateUtil.beginOfWeek(DateUtil.date()).toString(DatePattern.NORM_DATETIME_MS_PATTERN);
            }else if (week == 1) {
                afterDayDate = DateUtils.getAfterDayDate(DateUtil.beginOfWeek(new Date()), -7);
                tempDate = DateUtil.beginOfWeek(afterDayDate).toString(DatePattern.NORM_DATETIME_MS_PATTERN);
            }else if (week == 2) {
                afterDayDate = DateUtils.getAfterDayDate(DateUtil.beginOfWeek(new Date()), -14);
                tempDate = DateUtil.beginOfWeek(afterDayDate).toString(DatePattern.NORM_DATETIME_MS_PATTERN);
            }else if (week == 3) {
                afterDayDate = DateUtils.getAfterDayDate(DateUtil.beginOfWeek(new Date()), -21);
                tempDate = DateUtil.beginOfWeek(afterDayDate).toString(DatePattern.NORM_DATETIME_MS_PATTERN);
            }
        }

        // 临时医嘱
        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                .eqIfPresent(DialysisAdviceDO::getAdviceId, dialysisAdviceDO.getAdviceId())
                .eqIfPresent(DialysisAdviceDO::getPatientId, dialysisAdviceDO.getPatientId())
                .eq(DialysisAdviceDO::getLongAdviceId,createReqVO.getAdviceIds().split(",")[0])
                .eq(DialysisAdviceDO::getDeleted,0)
                .eq(DialysisAdviceDO::getType,"0")
                .isNotNull(DialysisAdviceDO::getPushStatus)
                .in(DialysisAdviceDO::getPushStatus,1)
                .apply("advice_time >='" + tempDate + "'"));
        for (int i = 1; i <= 4; i++) {
            DrainTeamAlreadyVO teamAlreadyVO = new DrainTeamAlreadyVO();
            int finalI = i;

            DateTime dateTime1 = DateUtil.offsetWeek(afterDayDate, i - 1);
            teamAlreadyVO.setClassesTime(DateUtil.beginOfWeek(dateTime1).toString(DatePattern.NORM_DATE_PATTERN));
            if (i == week + 1 && week > -1) {
                teamAlreadyVO.setClassesTime(DateUtil.beginOfWeek(dateTime1).toString(DatePattern.NORM_DATE_PATTERN) + "(本周)");
            }

            if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
                List<ArrangeClassesDO> arrangeClassesDOList = arrangeClassesDOS.stream().filter(arrangeClassesDO -> finalI == arrangeClassesDO.getTempType()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(arrangeClassesDOList)) {
                    for (ArrangeClassesDO arrangeClassesDO : arrangeClassesDOList) {
                        String classesTime = teamAlreadyVO.getClassesTime();
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        Date parse = new Date();
                        try {
                            parse  = simpleDateFormat.parse(classesTime);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        switch (arrangeClassesDO.getWeekState()) {
                            case 1:
                                teamAlreadyVO.setMonday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                teamAlreadyVO.setMondayDate(teamAlreadyVO.getClassesTime());
                                Date date1 = org.apache.commons.lang3.time.DateUtils.addDays(parse, 0);
                                if (!CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                                    List<DialysisAdviceDO> collect1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO1 -> dialysisAdviceDO1.getAdviceTime().equals(date1)).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(collect1)) {
                                        teamAlreadyVO.setMondayStatus("1");
                                    }
                                }
                                break;
                            case 2:
                                teamAlreadyVO.setTuesday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                Date date2 = org.apache.commons.lang3.time.DateUtils.addDays(parse, 1);
                                String format2 = simpleDateFormat.format(date2);
                                teamAlreadyVO.setTuesdayDate(format2);
                                if (!CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                                    List<DialysisAdviceDO> collect1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO1 -> dialysisAdviceDO1.getAdviceTime().equals(date2)).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(collect1)) {
                                        teamAlreadyVO.setTuesdayStatus("1");
                                    }
                                }
                                break;
                            case 3:
                                teamAlreadyVO.setWednesday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                Date date3 = org.apache.commons.lang3.time.DateUtils.addDays(parse, 2);
                                String format3 = simpleDateFormat.format(date3);
                                teamAlreadyVO.setWednesdayDate(format3);
                                if (!CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                                    List<DialysisAdviceDO> collect1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO1 -> dialysisAdviceDO1.getAdviceTime().equals(date3)).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(collect1)) {
                                        teamAlreadyVO.setWednesdayStatus("1");
                                    }
                                }
                                break;
                            case 4:
                                teamAlreadyVO.setThursday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                Date date4 = org.apache.commons.lang3.time.DateUtils.addDays(parse, 3);
                                String format4 = simpleDateFormat.format(date4);
                                teamAlreadyVO.setThursdayDate(format4);
                                if (!CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                                    List<DialysisAdviceDO> collect1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO1 -> dialysisAdviceDO1.getAdviceTime().equals(date4)).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(collect1)) {
                                        teamAlreadyVO.setThursdayStatus("1");
                                    }
                                }
                                break;
                            case 5:
                                teamAlreadyVO.setFriday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                Date date5 = org.apache.commons.lang3.time.DateUtils.addDays(parse, 4);
                                String format5 = simpleDateFormat.format(date5);
                                teamAlreadyVO.setFridayDate(format5);
                                if (!CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                                    List<DialysisAdviceDO> collect1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO1 -> dialysisAdviceDO1.getAdviceTime().equals(date5)).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(collect1)) {
                                        teamAlreadyVO.setFridayStatus("1");
                                    }
                                }
                                break;
                            case 6:
                                teamAlreadyVO.setSaturday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                Date date6 = org.apache.commons.lang3.time.DateUtils.addDays(parse, 5);
                                String format6 = simpleDateFormat.format(date6);
                                teamAlreadyVO.setSaturdayDate(format6);
                                if (!CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                                    List<DialysisAdviceDO> collect1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO1 -> dialysisAdviceDO1.getAdviceTime().equals(date6)).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(collect1)) {
                                        teamAlreadyVO.setSaturdayStatus("1");
                                    }
                                }
                                break;
                            case 7:
                                teamAlreadyVO.setSunday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                Date date7 = org.apache.commons.lang3.time.DateUtils.addDays(parse, 6);
                                String format7 = simpleDateFormat.format(date7);
                                teamAlreadyVO.setSundayDate(format7);
                                if (!CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                                    List<DialysisAdviceDO> collect1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO1 -> dialysisAdviceDO1.getAdviceTime().equals(date7)).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(collect1)) {
                                        teamAlreadyVO.setSundayStatus("1");
                                    }
                                }
                                break;
                        }
                    }
                }
            }
            map.put(i + "", teamAlreadyVO);
        }

        if (!CollectionUtils.isEmpty(dialysisAdviceDOS)) {
            DialysisAdviceDO dialysisAdviceDO1 = dialysisAdviceDOS.get(0);
            Map<String, Object> map2 = Maps.newHashMap();
            map2.put("startTime", dialysisAdviceDO1.getStartTime() != null ? DateUtil.format(dialysisAdviceDO1.getStartTime(), DatePattern.NORM_TIME_PATTERN) : null);
            map2.put("frequency", dialysisAdviceDO1.getFrequency());
            map2.put("medicateState", dialysisAdviceDO.getMedicateState());
            map.put("pushInfo", map2);
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void brainPower(List<ArrangeClassesCreateReqVO> createReqVO, HttpServletRequest request) {
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:brainPower:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                for (ArrangeClassesCreateReqVO arrangeClassesCreateReqVO : createReqVO) {
                    ArrangeClassesDO classesDO = ArrangeClassesConvert.INSTANCE.convert(arrangeClassesCreateReqVO);
                    if (classesDO.getFacilityId() != null) {
                        if (StrUtil.isEmpty(classesDO.getDialysisValue())) {
                            throw new ServiceException(GlobalErrorCodeConstants.DIALYZE_VALUE_NOT_EMTY);
                        }
                        List<DisinfectionPlanDO> disinfectionPlanDOS = disinfectionPlanMapper.selectList(new LambdaQueryWrapperX<DisinfectionPlanDO>()
                                .eqIfPresent(DisinfectionPlanDO::getFacilityId, classesDO.getFacilityId())
                                .eqIfPresent(DisinfectionPlanDO::getWeekDay, classesDO.getDayState())
                                .eqIfPresent(DisinfectionPlanDO::getDisinfectionTime, classesDO.getWeekState()));
                        FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, classesDO.getFacilityId());
                        if (CollectionUtil.isEmpty(disinfectionPlanDOS) || facilityManagerDO == null) {
                            throw new ServiceException(GlobalErrorCodeConstants.NO_PLAN);
                        }
                        if (facilityManagerDO == null) {
                            throw new ServiceException(GlobalErrorCodeConstants.NO_PLAN);
                        }
                        FacilityDO facilityDO = facilityMapper.selectById(arrangeClassesCreateReqVO.getFacilityId());
                        FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(arrangeClassesCreateReqVO.getFacilitySubareaId());
                        PatientDO patientDO = patientMapper.selectById(arrangeClassesCreateReqVO.getPatientId());
                        classesDO.setFacilityName(facilityDO != null ? facilityDO.getCode() : null);
                        classesDO.setFaciitySubareaName(facilitySubareaDO != null ? facilitySubareaDO.getName() : null);
                        if (patientDO != null) {
                            classesDO.setLabels(patientDO.getLabels());
                            classesDO.setPatientSrouce(patientDO.getPatientSource());
                            classesDO.setInfects(patientDO.getInfect());
                        }
                        DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", classesDO.getDialysisValue());
                        classesDO.setDialysisName(dialyzeWay != null ? dialyzeWay.getLabel() : "");
                        classesDO.setDialysisValue(StrUtil.isNotEmpty(classesDO.getDialysisValue()) ? classesDO.getDialysisValue() : "");
                        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                                .eq(ArrangeClassesDO::getTempType, classesDO.getTempType())
                                .eq(ArrangeClassesDO::getWeekState, classesDO.getWeekState())
                                .eq(ArrangeClassesDO::getDayState, classesDO.getDayState())
                                .eq(ArrangeClassesDO::getFacilityId, classesDO.getFacilityId())
                                .eq(ArrangeClassesDO::getFacilitySubareaId, classesDO.getFacilitySubareaId()));
                        ArrangeClassesDO arrangeClassesDO2 = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                                .eq(ArrangeClassesDO::getTempType, classesDO.getTempType())
                                .eq(ArrangeClassesDO::getWeekState, classesDO.getWeekState())
                                .eq(ArrangeClassesDO::getPatientId, classesDO.getPatientId()));
                        if (arrangeClassesDO != null && arrangeClassesDO2 != null) {//修改
                            if (!arrangeClassesDO.getPatientId().equals(classesDO.getPatientId())) {
                                ArrangeClassesDO arrange = new ArrangeClassesDO();
                                arrange.setTempType(arrangeClassesDO2.getTempType());
                                arrange.setWeekState(arrangeClassesDO2.getWeekState());
                                arrange.setDayState(arrangeClassesDO2.getDayState());
                                arrange.setPatientId(arrangeClassesDO2.getPatientId());
                                arrangeClassesDO2.setFacilityId(classesDO.getFacilityId());
                                arrangeClassesDO2.setFacilitySubareaId(classesDO.getFacilitySubareaId());
                                arrangeClassesDO2.setWeekState(classesDO.getWeekState());
                                arrangeClassesDO2.setDayState(classesDO.getDayState());
                                arrangeClassesMapper.update(arrangeClassesDO2, new LambdaUpdateWrapper<ArrangeClassesDO>()
                                        .eq(ArrangeClassesDO::getTempType, arrange.getTempType())
                                        .eq(ArrangeClassesDO::getWeekState, arrange.getWeekState())
                                        .eq(ArrangeClassesDO::getDayState, arrange.getDayState())
                                        .eq(ArrangeClassesDO::getPatientId, arrange.getPatientId()));
                                arrangeClassesMapper.deleteById(arrangeClassesDO.getId());
                            }
                        } else if (arrangeClassesDO != null && arrangeClassesDO2 == null) {//修改
                            arrangeClassesMapper.update(classesDO, new LambdaUpdateWrapper<ArrangeClassesDO>()
                                    .eq(ArrangeClassesDO::getTempType, arrangeClassesDO.getTempType())
                                    .eq(ArrangeClassesDO::getWeekState, arrangeClassesDO.getWeekState())
                                    .eq(ArrangeClassesDO::getDayState, arrangeClassesDO.getDayState())
                                    .eq(ArrangeClassesDO::getFacilityId, arrangeClassesDO.getFacilityId())
                                    .eq(ArrangeClassesDO::getFacilitySubareaId, arrangeClassesDO.getFacilitySubareaId()));
                        } else if (arrangeClassesDO2 != null) {//修改
                            ArrangeClassesDO arrange = new ArrangeClassesDO();
                            arrange.setTempType(arrangeClassesDO2.getTempType());
                            arrange.setWeekState(arrangeClassesDO2.getWeekState());
                            arrange.setDayState(arrangeClassesDO2.getDayState());
                            arrange.setPatientId(arrangeClassesDO2.getPatientId());

                            arrangeClassesDO2.setFacilityId(classesDO.getFacilityId());
                            arrangeClassesDO2.setFacilitySubareaId(classesDO.getFacilitySubareaId());
                            arrangeClassesDO2.setWeekState(classesDO.getWeekState());
                            arrangeClassesDO2.setDayState(classesDO.getDayState());
                            arrangeClassesMapper.update(arrangeClassesDO2, new LambdaUpdateWrapper<ArrangeClassesDO>()
                                    .eq(ArrangeClassesDO::getTempType, arrange.getTempType())
                                    .eq(ArrangeClassesDO::getWeekState, arrange.getWeekState())
                                    .eq(ArrangeClassesDO::getDayState, arrange.getDayState())
                                    .eq(ArrangeClassesDO::getPatientId, arrange.getPatientId()));
                        } else {//新增
                            classesDO.setClassesTime(null);
                            arrangeClassesMapper.insert(classesDO);
                        }
                    }
                }
            } else {
                return;
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("智能排班信息异常:{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    @Override
    public Map<String, Object> brainPowerList(ArrangeClassesCreateReqVO createReqVO) {
        PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
        Map<String, Object> map = Maps.newHashMap();
        for (int i = 1; i <= 4; i++) {
            List<BrainPowerCreateReqVO> list = Lists.newArrayList();
            List<Integer> weekStates = Lists.newArrayList();
            //Map<Integer, Date> dateMap = Maps.newHashMap();
            BrainPowerRespVO brainPowerRespVO = new BrainPowerRespVO();
            //计算每个模版时间
            //Date startWeek = null;
            //Date endWeek = null;
            //if (1 == i) {
            //    startWeek = DateUtil.beginOfWeek(new Date());
            //    endWeek = DateUtil.endOfWeek(DateUtil.offsetWeek(new Date(), i - 1));
            //} else {
            //    DateTime dateTime = DateUtil.offsetWeek(new Date(), i - 1);
            //    startWeek = DateUtil.beginOfWeek(dateTime);
            //    endWeek = DateUtil.endOfWeek(dateTime);
            //}
            //DateRange range = DateUtil.range(startWeek, endWeek, DateField.DAY_OF_WEEK);
            //for (DateTime dateTime : range) {
            //    int w = DateUtil.dayOfWeek(dateTime) - 1 == 0 ? 7 : DateUtil.dayOfWeek(dateTime) - 1;
            //    dateMap.put(w, dateTime);
            //}
            for (int j = 1; j <= 7; j++) {
                BrainPowerCreateReqVO createReqVO1 = new BrainPowerCreateReqVO();
                //for (Map.Entry<Integer, Date> integerDateEntry : dateMap.entrySet()) {
                //    if (j == integerDateEntry.getKey()) {
                //        createReqVO1.setClassesTime(DateUtil.beginOfDay(integerDateEntry.getValue()));
                //        Week week = DateUtil.dayOfWeekEnum(integerDateEntry.getValue());
                //        String weekStr = week.toChinese("周");
                //        createReqVO1.setWeekName(weekStr);
                //    }
                //}
                ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                        .eqIfPresent(ArrangeClassesDO::getTempType, i)
                        .eqIfPresent(ArrangeClassesDO::getWeekState, j));
                if (arrangeClassesDO == null) {
                    ArrangeClassesDO arrangeClassesDO1 = new ArrangeClassesDO();
                    BeanUtil.copyProperties(arrangeClassesDO1, createReqVO1);
                } else {
                    BeanUtil.copyProperties(arrangeClassesDO, createReqVO1);
                    List<FacilityDO> facilityDOS = facilityMapper.selectList(FacilityDO::getSubareaId, arrangeClassesDO.getFacilitySubareaId());
                    if (CollectionUtil.isNotEmpty(facilityDOS)) {
                        createReqVO1.setFacilityList(facilityDOS);
                    }
                }
                createReqVO1.setDisabled(1);
                weekStates.add(j);
                createReqVO1.setWeekState(j);
                createReqVO1.setTempType(i);
                createReqVO1.setPatientId(createReqVO.getPatientId());
                createReqVO1.setPatientName(patientDO != null ? patientDO.getName() : null);
                list.add(createReqVO1);
            }
            brainPowerRespVO.setParams(list);
            brainPowerRespVO.setWeekStates(weekStates);
            map.put(i + "", brainPowerRespVO);
        }
        return map;
    }

    @Override
    public List<FacilityRespVO> getClassFacilityList(ArrangeClassesCreateReqVO createReqVO) {
        createReqVO.setPatientId(null);
        List<FacilityDO> facilityDOS = facilityMapper.selectList(FacilityDO::getSubareaId, createReqVO.getFacilitySubareaId());
        return FacilityConvert.INSTANCE.convertList(facilityDOS);
        //List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
        //        .eqIfPresent(ArrangeClassesDO::getTempType, createReqVO.getTempType())
        //        .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
        //        .eqIfPresent(ArrangeClassesDO::getWeekState, createReqVO.getWeekState())
        //        .eqIfPresent(ArrangeClassesDO::getDayState, createReqVO.getDayState())
        //        .eqIfPresent(ArrangeClassesDO::getFacilitySubareaId, createReqVO.getFacilitySubareaId()));
        //if (CollectionUtil.isNotEmpty(facilityRespVOS)) {
        //    if (CollectionUtil.isEmpty(arrangeClassesDOS)) {
        //        return facilityRespVOS;
        //    }
        //    List<Long> facilityIds = arrangeClassesDOS.stream().map(ArrangeClassesDO::getFacilityId).collect(Collectors.toList());
        //    return facilityRespVOS.stream().filter(facilityRespVO -> !facilityIds.contains(facilityRespVO.getId())).collect(Collectors.toList());
        //}
        //return null;
    }

    @Override
    public ArrangeClassesRespVO getTeamPatient(ArrangeClassesCreateReqVO createReqVO) {
        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
                .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0));
        ArrangeClassesRespVO convert = ArrangeClassesConvert.INSTANCE.convert(arrangeClassesDO);
        // 查询第一次记录
        DialysisDetectionDO dialysisDetectionDO = dialysisDetectionMapper.selectOne(new LambdaQueryWrapperX<DialysisDetectionDO>()
                .eqIfPresent(DialysisDetectionDO::getPatientId, createReqVO.getPatientId())
                .eqIfPresent(DialysisDetectionDO::getDateWeek, createReqVO.getClassesTime())
                .eq(DialysisDetectionDO::getDialyzeState, 0)
                .eq(DialysisDetectionDO::getDeleted, 0));
        if (!StringUtils.isEmpty(dialysisDetectionDO)) {
            Date detectionMin = dialysisDetectionDO.getDetectionMin();
            Calendar instance = Calendar.getInstance();
            instance.setTime(detectionMin);

            HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                    .eqIfPresent(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                    .eqIfPresent(HemodialysisManagerDO::getHemodialysisTime, createReqVO.getClassesTime())
                    .eq(HemodialysisManagerDO::getDeleted, 0));
            if (!StringUtils.isEmpty(hemodialysisManagerDO)) {
                if ( (org.springframework.util.StringUtils.isEmpty(hemodialysisManagerDO.getDurationMin()) || "00".equals(hemodialysisManagerDO.getDurationMin()))){
                    hemodialysisManagerDO.setDurationMin("0");
                }
                String duration = hemodialysisManagerDO.getDuration();
                if (!StringUtils.isEmpty(hemodialysisManagerDO.getDuration())) {
                    if (hemodialysisManagerDO.getDuration().contains(".")) {
                        String[] split = hemodialysisManagerDO.getDuration().split(".");
                        duration = split[0];
                        if (split.length > 1) {
                            hemodialysisManagerDO.setDurationMin(String.valueOf(Integer.parseInt(split[1]) * 60));
                        }
                    }
                    instance.add(Calendar.HOUR_OF_DAY, Integer.parseInt(duration));
                    instance.add(Calendar.MINUTE, Integer.parseInt(hemodialysisManagerDO.getDurationMin()));
                    Date time = instance.getTime();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
                    String format = simpleDateFormat.format(time);
                    convert.setEndTimeMinNow(format);
                }

            }

        }

        return convert;
    }

    @Override
    public PageResult<ArrangeClassesRespVO> getHistoryPage(ArrangeClassesPageReqVO pageReqVO) {
        PageResult<ArrangeClassesRespVO> classesRespVOPageResult = new PageResult<>();
        pageReqVO.setPageNo((pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());
        List<ArrangeClassesRespVO> arrangeClassesRespVOList = arrangeClassesMapper.selectPageHistory(pageReqVO);
        Long count = arrangeClassesMapper.selectCountHistory(pageReqVO);
        classesRespVOPageResult.setList(arrangeClassesRespVOList);
        if (CollectionUtil.isNotEmpty(classesRespVOPageResult.getList())) {
            List<ArrangeClassesRespVO> collect = classesRespVOPageResult.getList().stream().peek(arrangeClassesRespVO -> {
                int week = DateUtil.weekOfYear(DateUtil.beginOfWeek(arrangeClassesRespVO.getClassesTime()));
                String format = DateUtil.format(arrangeClassesRespVO.getClassesTime(), DatePattern.NORM_DATE_PATTERN);
                Week weekEnum = DateUtil.dayOfWeekEnum(arrangeClassesRespVO.getClassesTime());
                String weekStr = weekEnum.toChinese("周");
                arrangeClassesRespVO.setClassesTimeStr(week + "周" + "(" + format + ")" + weekStr);
            }).collect(Collectors.toList());
            classesRespVOPageResult.setList(collect);
        }
        classesRespVOPageResult.setTotal(count);
        return classesRespVOPageResult;
    }

    @Override
    public void updateDeleted(ArrangeClassesUpdateReqVO updateReqVO) {
        Long count = arrangeClassesMapper.selectCount(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getWeekState, updateReqVO.getWeekState())
                .eq(ArrangeClassesDO::getDayState, updateReqVO.getDayState())
                .eq(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime())
                .eq(ArrangeClassesDO::getFacilityId, updateReqVO.getFacilityId())
                .eq(ArrangeClassesDO::getFacilitySubareaId, updateReqVO.getFacilitySubareaId()));
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK_EXIT_PATIENT);
        }
        arrangeClassesMapper.updateDeleted(updateReqVO);
    }

    @Override
    public Map<String, Object> getPatientNum(String date) {
        Map<String, Object> map = Maps.newHashMap();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = simpleDateFormat.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MILLISECOND,0);
        Date time = calendar.getTime();

        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(ArrangeClassesDO::getClassesTime, time);
        map.put("a",0);
        map.put("b",0);
        map.put("c",0);
        map.put("d",0);

        if (!CollectionUtils.isEmpty(arrangeClassesDOS)) {
            List<ArrangeClassesDO> collect1 = arrangeClassesDOS.stream().filter(arrangeClassesDO -> "a".equals(arrangeClassesDO.getDayState())).collect(Collectors.toList());
            map.put("a",collect1.size());

            List<ArrangeClassesDO> collect2 = arrangeClassesDOS.stream().filter(arrangeClassesDO -> "b".equals(arrangeClassesDO.getDayState())).collect(Collectors.toList());
            map.put("b",collect2.size());

            List<ArrangeClassesDO> collect3 = arrangeClassesDOS.stream().filter(arrangeClassesDO -> "c".equals(arrangeClassesDO.getDayState())).collect(Collectors.toList());
            map.put("c",collect3.size());

            map.put("d",collect1.size() + collect2.size() + collect3.size());
        }

        return map;
    }

    private List<DrainTeamAlreadyVO> weekStateList(List<ArrangeClassesDO> arrangeClassesDOS, List<DrainTeamAlreadyVO> teamAlreadyVOS) {
        List<DictDataRespDTO> arrangeClassesPeriodTime = dictDataApi.getDictListData("arrangeClassesPeriodTime");
        if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
            for (DictDataRespDTO dictDataRespDTO : arrangeClassesPeriodTime) {
                DrainTeamAlreadyVO drainTeamAlreadyVO = new DrainTeamAlreadyVO();
                drainTeamAlreadyVO.setDayState(dictDataRespDTO.getLabel());
                if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
                    List<ArrangeClassesDO> mornings = arrangeClassesDOS.stream().filter(arrangeClassesDO -> dictDataRespDTO.getValue().equals(arrangeClassesDO.getDayState())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(mornings)) {
                        for (ArrangeClassesDO arrangeClassesDO : mornings) {
                            switch (arrangeClassesDO.getWeekState()) {
                                case 1:
                                    drainTeamAlreadyVO.setMonday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 2:
                                    drainTeamAlreadyVO.setTuesday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 3:
                                    drainTeamAlreadyVO.setWednesday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 4:
                                    drainTeamAlreadyVO.setThursday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 5:
                                    drainTeamAlreadyVO.setFriday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 6:
                                    drainTeamAlreadyVO.setSaturday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 7:
                                    drainTeamAlreadyVO.setSunday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                            }
                        }
                    }
                }
                teamAlreadyVOS.add(drainTeamAlreadyVO);
            }
        }
        return teamAlreadyVOS;
    }
}
