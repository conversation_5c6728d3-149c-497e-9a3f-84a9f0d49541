package com.thj.boot.module.business.controller.admin.arrangeclasses;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.thj.boot.common.annotation.OperateLog;
import com.thj.boot.common.annotation.RepeatSubmit;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.EasyExcelUtils;
import com.thj.boot.module.business.controller.admin.arrangeclasses.vo.DialyzeBudgeExeclVO;
import com.thj.boot.module.business.controller.admin.drainteam.vo.DialyzBudgetRespVO;
import com.thj.boot.module.business.convert.arrangeclasses.ArrangeClassesConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.*;
import com.thj.boot.module.business.pojo.facility.vo.FacilityRespVO;
import com.thj.boot.module.business.service.arrangeclasses.ArrangeClassesService;
import com.thj.boot.module.business.service.facility.FacilityService;
import com.thj.boot.module.business.service.facilitysubarea.FacilitySubareaService;
import com.thj.boot.module.business.service.jkimplementation.JkImplementationService;
import com.thj.boot.module.system.dal.datado.dict.DictDataDO;
import com.thj.boot.module.system.pojo.dict.vo.data.DictDataExportReqVO;
import com.thj.boot.module.system.service.dict.DictDataService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 新排班
 */
@RestController
@RequestMapping("/business/arrange-classes")
@Validated
@ConditionalOnProperty(name = "spring.redis.enabled", havingValue = "true", matchIfMissing = true)
public class ArrangeClassesController {

    @Resource
    private ArrangeClassesService arrangeClassesService;

    @Resource
    private JkImplementationService jkImplementationService;

    @Resource
    private FacilityService facilityService;

    @Resource
    private FacilitySubareaService facilitySubareaService;

    @Resource
    private DictDataService dictDataService;


    /**
     * 根据患者id获取排班的详细信息
     */
    @GetMapping("/getPatientNum/{date}")
    public CommonResult<Map<String, Object>> getPatientNum(@PathVariable("date") String date)
    {
        return success(arrangeClassesService.getPatientNum(date));
    }

    /**
     * 根据患者id获取排班的详细信息
     */
    @GetMapping("/teamPatientInfo")
    public CommonResult<Map<String, Object>> teamPatientInfo(Long patientId) {
        return success(arrangeClassesService.teamPatientInfo(patientId));
    }


    /**
     * 透析预算
     */
    @PostMapping("/dialyzBudgePage")
    public CommonResult<Map<String, Object>> dialyzBudgePage(@RequestBody ArrangeClassesCreateReqVO createVO) {
        return success(arrangeClassesService.dialyzBudgePage(createVO));
    }


    /**
     * 导出
     */
    @PostMapping("/export-excel")
    public void exportDevManExcel(@RequestBody ArrangeClassesCreateReqVO exportReqVO,
                                  HttpServletResponse response) throws IOException {
        Map<String, Object> map = arrangeClassesService.dialyzBudgePage(exportReqVO);
        List<DialyzBudgetRespVO> dialyzBudgetRespVOS = (List<DialyzBudgetRespVO>) map.get("budgetRespVOS");
        List<DialyzeBudgeExeclVO> dialyzeBudgeExeclVOS = ArrangeClassesConvert.INSTANCE.convertListExcel(dialyzBudgetRespVOS);
        EasyExcelUtils.write(response, "透析预算.xls", "透析预算", DialyzeBudgeExeclVO.class, dialyzeBudgeExeclVOS);
    }

    /**
     * 血液透析左侧患者列表
     */
    @PostMapping("/patientList")
    public CommonResult<List<ArrangeClassesRespVO>> patientList(@RequestBody ArrangeClassesCreateReqVO createVO) {
        return success(arrangeClassesService.patientList(createVO));
    }

    /**
     * 获取日历相关信息
     */
    @PostMapping("/getCalendars")
    public CommonResult<Map<String, Object>> getCalendars(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        return success(arrangeClassesService.getCalendars(createReqVO));
    }

    /**
     * APP获取每月排班相关信息
     */
    @PostMapping("/getMonthCalendars")
    public CommonResult<List<ArrangeClassesRespVO>> getMonthCalendars(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        return success(arrangeClassesService.getMonthCalendars(createReqVO));
    }

    /**
     * 表头
     */
    @PostMapping("/tableHeadList")
    public CommonResult<List<ArrangeClassesRespVO>> tableHeadList(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        return success(arrangeClassesService.tableHeadList(createReqVO));
    }

    /**
     * 表体
     */
    @PostMapping("/tableContentList")
    public CommonResult<Map<Long, List<Map<String, Object>>>> tableContentList(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        return success(arrangeClassesService.tableContentList(createReqVO));
    }

    /**
     * 拖动检测提示语
     */
    @PostMapping("/checkPrompt")
    public CommonResult<List<String>> checkPrompt(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        return success(arrangeClassesService.checkPrompt(createReqVO));
    }

    /**
     * 检查更新状态
     * @return
     */
    @PostMapping("/checkUpdateStatus")
    public CommonResult<Integer> checkUpdateStatus(@RequestBody ArrangeClassesCreateReqVO createReqVO){
        return success(arrangeClassesService.checkUpdateStatus(createReqVO));
    }

    /**
     * 更新排班
     * @return
     */
    @PostMapping("/updateArrangeClasses")
    public CommonResult<Boolean> updateArrangeClasses(@RequestBody ArrangeClassesCreateReqVO createReqVO, HttpServletRequest request){
        arrangeClassesService.updateArrangeClasses(createReqVO, request);
        return success(true);
    }

    /**
     * 批量修改
     */
    @PostMapping("/batchUpdate")
    public CommonResult<Boolean> batchUpdateClasses(@RequestBody ArrangeClassesUpdateReqVO updateReqVO, HttpServletRequest request) {
        arrangeClassesService.batchUpdateClasses(updateReqVO, request);
        return success(true);
    }

    /**
     * 导入到模板排班
     */
    @PostMapping("/import/MB-classes")
    public CommonResult<Boolean> importMBClasses(@RequestBody ArrangeClassesUpdateReqVO updateReqVO, HttpServletRequest request) {
        arrangeClassesService.importMBClasses(updateReqVO, request);
        return success(true);
    }

    /**
     * 预约排班 的导出
     */
    @PostMapping("/export/arrangeClass")
    public void exportArrangeClass(@RequestBody ArrangeClassesCreateReqVO createReqVO, HttpServletResponse response) throws IOException {
        // 获取头部信息
        Map<String, Date> dateMap = arrangeClassesService.tableHeadList(createReqVO).stream().collect(Collectors.toMap(ArrangeClassesRespVO::getWeekName, ArrangeClassesRespVO::getClassesTime));
//        // 组装头部信息
//        List<List<String>> headTitles = Lists.newArrayList();
//        // 固定title
//        headTitles.add(Lists.newArrayList("分区", "分区"));
//        headTitles.add(Lists.newArrayList("机号", "机号"));
//        List<String> paymentTitles = Lists.newArrayList("上午", "下午", "晚上");
//        for (ArrangeClassesRespVO arrangeClassesRespVO : headList) {
//            for (String paymentTitle : paymentTitles) {
//                headTitles.add(Lists.newArrayList(
//                        arrangeClassesRespVO.getWeekName() + "（" + arrangeClassesRespVO.getClassesTime() + "）",
//                        paymentTitle));
//            }
//        }
//        headTitles.add(Lists.newArrayList("总数", "总数"));
        Map<ExcelProperty, String[]> headMap = new LinkedHashMap<>();
        Field[] declaredFields = ArrangeClassExcelVO.class.getDeclaredFields();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (Field field : declaredFields) {
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (annotation == null) continue;
            String[] value = annotation.value();
            if (value.length > 0) {
                String dynamicValue = value[0];
                if (dateMap.containsKey(dynamicValue)) {
                    dynamicValue = dynamicValue.replace(dynamicValue, dynamicValue + "（" + dateFormat.format(dateMap.get(dynamicValue)) + "）");
                }
                value[0] = dynamicValue;
                headMap.put(annotation, value);
            }
        }

        // 构建excel数据
        List<ArrangeClassExcelVO> arrangeClassVOS = new ArrayList<>();
        Map<String, Map<String, Integer>> colCounts = new HashMap<>();
        String[] weekDays = {"1", "2", "3", "4", "5", "6", "7"};
        String[] dayStatus = {"a", "b", "c"};
        Map<String, BiConsumer<ArrangeClassExcelVO, String>> setterMap = new HashMap<>();
        setterMap.put("1a", ArrangeClassExcelVO::setMonA);
        setterMap.put("1b", ArrangeClassExcelVO::setMonB);
        setterMap.put("1c", ArrangeClassExcelVO::setMonC);
        setterMap.put("2a", ArrangeClassExcelVO::setTueA);
        setterMap.put("2b", ArrangeClassExcelVO::setTueB);
        setterMap.put("2c", ArrangeClassExcelVO::setTueC);
        setterMap.put("3a", ArrangeClassExcelVO::setWedA);
        setterMap.put("3b", ArrangeClassExcelVO::setWedB);
        setterMap.put("3c", ArrangeClassExcelVO::setWedC);
        setterMap.put("4a", ArrangeClassExcelVO::setThuA);
        setterMap.put("4b", ArrangeClassExcelVO::setThuB);
        setterMap.put("4c", ArrangeClassExcelVO::setThuC);
        setterMap.put("5a", ArrangeClassExcelVO::setFriA);
        setterMap.put("5b", ArrangeClassExcelVO::setFriB);
        setterMap.put("5c", ArrangeClassExcelVO::setFriC);
        setterMap.put("6a", ArrangeClassExcelVO::setSatA);
        setterMap.put("6b", ArrangeClassExcelVO::setSatB);
        setterMap.put("6c", ArrangeClassExcelVO::setSatC);
        setterMap.put("7a", ArrangeClassExcelVO::setSunA);
        setterMap.put("7b", ArrangeClassExcelVO::setSunB);
        setterMap.put("7c", ArrangeClassExcelVO::setSunC);

        // 获取数据信息
        Map<Long, List<Map<String, Object>>> tableContentMap = arrangeClassesService.tableContentList(createReqVO);
        Map<Long, List<Map<String, Object>>> effectiveTableContentMap = MapUtil.newHashMap();
        if (CollUtil.isNotEmpty(createReqVO.getSubareaId()) || CollUtil.isNotEmpty(createReqVO.getClasses())) {
            // 过滤分区
            createReqVO.getSubareaId().remove("all");
            if (CollUtil.isNotEmpty(createReqVO.getSubareaId())) {
                for (String subareaId : createReqVO.getSubareaId()) {
                    effectiveTableContentMap.put(Long.parseLong(subareaId), tableContentMap.get(Long.parseLong(subareaId)));
                }
                tableContentMap = effectiveTableContentMap;
            }

            // 过滤班次
            createReqVO.getClasses().remove("all");
            if (CollUtil.isNotEmpty(createReqVO.getClasses())) {
                for (String classes : createReqVO.getClasses()) {
                    effectiveTableContentMap.forEach((key, value) -> {
                        value.forEach(map -> {
                            for (String weekStatus : weekDays) {
                                for (String status : dayStatus) {
                                    String keyMap = weekStatus + status;
                                    String filterKeyMap = weekStatus + classes;
                                    if (!keyMap.equals(filterKeyMap)) {
                                        map.put(keyMap, "&nbsp;");
                                    }
                                }
                            }
                        });
                    });
                }
                tableContentMap = effectiveTableContentMap;
            }
        }

        AtomicInteger allRowCount = new AtomicInteger();
        tableContentMap.forEach((key, value) -> {
            if (CollUtil.isNotEmpty(value)) {
                value.forEach(item -> {
                    int rowCount = 0;
                    ArrangeClassExcelVO arrangeClassExcelVO = new ArrangeClassExcelVO();
                    arrangeClassExcelVO.setFaciitySubareaName((String) item.get("faciitySubareaName"));
                    arrangeClassExcelVO.setFacilityName((String) item.get("facilityName"));
                    for (String weekDay : weekDays) {
                        for (String status : dayStatus) {
                            String setterKey = weekDay + status;
                            Object data = item.get(setterKey);
                            if (data.equals("&nbsp;")) continue;
                            ArrangeClassesRespVO arrangeClassesRespVO = (ArrangeClassesRespVO) data;
                            BiConsumer<ArrangeClassExcelVO, String> setter = setterMap.get(setterKey);
                            if (setter != null) {
                                StringBuilder sb = new StringBuilder();
                                sb.append(arrangeClassesRespVO.getPatientName())
                                        .append(" ")
                                        .append(arrangeClassesRespVO.getDialysisName())
                                        .append("\n")
                                        .append(arrangeClassesRespVO.getConsumSpec() != null ? arrangeClassesRespVO.getConsumSpec() : "");
                                setter.accept(arrangeClassExcelVO, sb.toString());
                            }
                            // 统计行
                            rowCount++;
                            // 统计列
                            if (!colCounts.containsKey(setterKey)) colCounts.put(setterKey, MapUtil.newHashMap());
                            Map<String, Integer> colCountsMap = colCounts.get(setterKey);
                            if (colCountsMap.containsKey(arrangeClassesRespVO.getDialysisValue())) {
                                colCountsMap.put(arrangeClassesRespVO.getDialysisValue(), colCountsMap.get(arrangeClassesRespVO.getDialysisValue()) + 1);
                            } else {
                                colCountsMap.put(arrangeClassesRespVO.getDialysisValue(), 1);
                            }
                        }
                    }
                    allRowCount.addAndGet(rowCount);
                    arrangeClassExcelVO.setRowTotal(rowCount);
                    arrangeClassVOS.add(arrangeClassExcelVO);
                });
            }
        });

        // 获取透析方式字典数据
        DictDataExportReqVO reqVO = new DictDataExportReqVO();
        reqVO.setDictType("dialyze_way");
        Map<String, String> dialysisMethodMap = dictDataService.getDictDataList(reqVO).stream().collect(Collectors.toMap(DictDataDO::getValue, DictDataDO::getLabel));

        // 最后一行 （统计行）
        ArrangeClassExcelVO arrangeClassExcelVO = new ArrangeClassExcelVO();
        arrangeClassExcelVO.setFaciitySubareaName("总数");
        arrangeClassExcelVO.setFacilityName(String.valueOf(arrangeClassVOS.size()));
        for (String weekDay : weekDays) {
            for (String status : dayStatus) {
                String setterKey = weekDay + status;
                BiConsumer<ArrangeClassExcelVO, String> setter = setterMap.get(setterKey);
                if (setter != null) {
                    Map<String, Integer> colCountMap = colCounts.get(setterKey);
                    StringBuilder sb = new StringBuilder();
                    AtomicInteger allCount = new AtomicInteger();
                    if (colCountMap != null) {
                        colCountMap.forEach((key, value) -> {
                            sb.append(dialysisMethodMap.get(key)).append(":").append(value).append("\n");
                            allCount.addAndGet(value);
                        });
                    }
                    sb.append(allCount);
                    setter.accept(arrangeClassExcelVO, sb.toString());
                }
            }
        }
        arrangeClassExcelVO.setRowTotal(allRowCount.get());
        arrangeClassVOS.add(arrangeClassExcelVO);

        // 创建WriteSheet
        WriteSheet writeSheet = EasyExcel.writerSheet("预约排班").build();
        // 构建表头信息
        List<List<String>> headTitles = new ArrayList<>();
        for (Map.Entry<ExcelProperty, String[]> entry : headMap.entrySet()) {
            headTitles.add(Lists.newArrayList(entry.getValue()));
        }

        // 导出Excel
        EasyExcel.write(response.getOutputStream(), ArrangeClassExcelVO.class)
                .head(headTitles)
                .sheet(createReqVO.getExcelTitle())
                .doWrite(arrangeClassVOS);
//        EasyExcelUtils.write(response, "预约排班表.xls", "预约排班表", ArrangeClassExcelVO.class, arrangeClassVOS);
    }

    /**
     * 新增
     */
    @PostMapping("/create")
    @OperateLog("新增排班")
    public CommonResult<Boolean> createArrangeClasses(@Valid @RequestBody ArrangeClassesCreateReqVO createReqVO, HttpServletRequest request) {
        arrangeClassesService.createArrangeClasses(createReqVO, request);
        try {
            jkImplementationService.save(createReqVO);
        } catch (Exception e) {
        }
        return success(true);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperateLog("修改排班")
    public CommonResult<Boolean> updateArrangeClasses(@Valid @RequestBody ArrangeClassesUpdateReqVO updateReqVO) {
        arrangeClassesService.updateArrangeClasses(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @OperateLog("删除排班")
    public CommonResult<Boolean> deleteArrangeClasses(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        arrangeClassesService.deleteArrangeClasses(createReqVO);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<ArrangeClassesRespVO> getArrangeClasses(@RequestParam("id") Long id) {
        ArrangeClassesDO arrangeClasses = arrangeClassesService.getArrangeClasses(id);
        return success(ArrangeClassesConvert.INSTANCE.convert(arrangeClasses));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<ArrangeClassesRespVO>> getArrangeClassesList(ArrangeClassesCreateReqVO createReqVO) {
        List<ArrangeClassesDO> list = arrangeClassesService.getArrangeClassesList(createReqVO);
        return success(ArrangeClassesConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<ArrangeClassesRespVO>> getArrangeClassesPage(@RequestBody ArrangeClassesPageReqVO pageVO) {
        PageResult<ArrangeClassesDO> pageResult = arrangeClassesService.getArrangeClassesPage(pageVO);
        return success(ArrangeClassesConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 用药推送-模版排班
     */
    @PostMapping("/tempPush")
    @OperateLog
    public CommonResult<Map<String, Object>> tempPush(@RequestBody ArrangeClassesCreateReqVO createReqVO, HttpServletRequest request) {
        return success(arrangeClassesService.tempPush(createReqVO,request));
    }

    /***
     * <AUTHOR>
     * @date 2024/3/23 9:12
     * @Description 智能排班
     **/
    @PostMapping("/brainPower")
    @RepeatSubmit
    public CommonResult<Boolean> brainPower(@RequestBody List<ArrangeClassesCreateReqVO> createReqVO, HttpServletRequest request) {
        arrangeClassesService.brainPower(createReqVO, request);
        return success(true);
    }


    /***
     * <AUTHOR>
     * @date 2024/3/23 9:12
     * @Description 智能排班查询
     **/
    @PostMapping("/brainPowerList")
    public CommonResult<Map<String, Object>> brainPowerList(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        return success(arrangeClassesService.brainPowerList(createReqVO));
    }

    /**
     * 过滤没有排班的机号
     */
    @PostMapping("/getClassFacilityList")
    public CommonResult<List<FacilityRespVO>> getClassFacilityList(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        return success(arrangeClassesService.getClassFacilityList(createReqVO));
    }

    /**
     * 冻结，解冻仅限于历史排班
     */
    @PostMapping("/getHistoryPage")
    public CommonResult<PageResult<ArrangeClassesRespVO>> getHistoryPage(@RequestBody ArrangeClassesPageReqVO pageReqVO) {
        return success(arrangeClassesService.getHistoryPage(pageReqVO));
    }

    /**
     * 解冻
     */
    @PostMapping("/updateDeleted")
    @OperateLog("解冻排班")
    public CommonResult<Boolean> updateDeleted(@RequestBody ArrangeClassesUpdateReqVO updateReqVO) {
        arrangeClassesService.updateDeleted(updateReqVO);
        return success(true);
    }


}
