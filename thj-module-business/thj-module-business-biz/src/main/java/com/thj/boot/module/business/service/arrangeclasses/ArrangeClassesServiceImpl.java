package com.thj.boot.module.business.service.arrangeclasses;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Maps;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.DateUtils;
import com.thj.boot.module.business.controller.admin.arrangeclasses.event.TeamLogEvent;
import com.thj.boot.module.business.controller.admin.arrangeclasses.vo.BrainPowerCreateReqVO;
import com.thj.boot.module.business.controller.admin.arrangeclasses.vo.BrainPowerRespVO;
import com.thj.boot.module.business.controller.admin.drainteam.vo.DialyzBudgetRespVO;
import com.thj.boot.module.business.controller.admin.drainteam.vo.DrainTeamAlreadyVO;
import com.thj.boot.module.business.convert.arrangeclasses.ArrangeClassesConvert;
import com.thj.boot.module.business.convert.facility.FacilityConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.dal.datado.dialysismanager.DialysisManagerDO;
import com.thj.boot.module.business.dal.datado.dialysisprotocol.DialysisProtocolDO;
import com.thj.boot.module.business.dal.datado.dialyzeoption.DialyzeOptionDO;
import com.thj.boot.module.business.dal.datado.diseasereason.DiseaseReasonDO;
import com.thj.boot.module.business.dal.datado.disinfectionplan.DisinfectionPlanDO;
import com.thj.boot.module.business.dal.datado.dryweight.DryWeightDO;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.dal.datado.facilityname.FacilityNameDO;
import com.thj.boot.module.business.dal.datado.facilitysubarea.FacilitySubareaDO;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.teamlog.TeamLogDO;
import com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper;
import com.thj.boot.module.business.dal.mapper.dialysisadvice.DialysisAdviceMapper;
import com.thj.boot.module.business.dal.mapper.dialysismanager.DialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.dialysisprotocol.DialysisProtocolMapper;
import com.thj.boot.module.business.dal.mapper.dialyzeoption.DialyzeOptionMapper;
import com.thj.boot.module.business.dal.mapper.diseasereason.DiseaseReasonMapper;
import com.thj.boot.module.business.dal.mapper.disinfectionplan.DisinfectionPlanMapper;
import com.thj.boot.module.business.dal.mapper.dryweight.DryWeightMapper;
import com.thj.boot.module.business.dal.mapper.facility.FacilityMapper;
import com.thj.boot.module.business.dal.mapper.facilitymanager.FacilityManagerMapper;
import com.thj.boot.module.business.dal.mapper.facilityname.FacilityNameMapper;
import com.thj.boot.module.business.dal.mapper.facilitysubarea.FacilitySubareaMapper;
import com.thj.boot.module.business.dal.mapper.hisconsumables.HisConsumablesMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.*;
import com.thj.boot.module.business.pojo.facility.vo.FacilityRespVO;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.UserCreateReqDTO;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新排班 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
@ConditionalOnProperty(name = "spring.redis.enabled", havingValue = "true", matchIfMissing = true)
public class ArrangeClassesServiceImpl implements ArrangeClassesService {

    @Resource
    private ArrangeClassesMapper arrangeClassesMapper;

    @Resource
    private FacilityMapper facilityMapper;

    @Resource
    private FacilitySubareaMapper subareaMapper;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private FacilityManagerMapper facilityManagerMapper;

    @Resource
    private DialyzeOptionMapper dialyzeOptionMapper;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DryWeightMapper dryWeightMapper;

    @Resource
    private DialysisManagerMapper dialysisManagerMapper;

    @Resource
    private DiseaseReasonMapper diseaseReasonMapper;

    @Resource
    private DisinfectionPlanMapper disinfectionPlanMapper;

    @Resource
    private FacilityNameMapper facilityNameMapper;

    @Resource
    private FacilitySubareaMapper facilitySubareaMapper;

    @Resource
    private DialysisAdviceMapper dialysisAdviceMapper;

    @Resource
    private DialysisProtocolMapper dialysisProtocolMapper;

    @Resource
    private HisConsumablesMapper hisConsumablesMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void createArrangeClasses(ArrangeClassesCreateReqVO createReqVO, HttpServletRequest request) {
        ArrangeClassesDO arrangeClasses = ArrangeClassesConvert.INSTANCE.convert(createReqVO);
        long loginIdAsLong = StpUtil.getLoginIdAsLong();
        if (loginIdAsLong != 1 && createReqVO.getTempType() == 0) {
            if (arrangeClasses.getClassesTime().before(DateUtil.beginOfDay(DateUtil.date()))) {
                throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK);
            }
        }

        List<DisinfectionPlanDO> disinfectionPlanDOS = disinfectionPlanMapper.selectList(new LambdaQueryWrapperX<DisinfectionPlanDO>()
                .eqIfPresent(DisinfectionPlanDO::getFacilityId, arrangeClasses.getFacilityId())
                .eqIfPresent(DisinfectionPlanDO::getWeekDay, arrangeClasses.getDayState())
                .eqIfPresent(DisinfectionPlanDO::getDisinfectionTime, arrangeClasses.getWeekState()));
        FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, arrangeClasses.getFacilityId());
        if (CollectionUtil.isEmpty(disinfectionPlanDOS)) {
            throw new ServiceException(GlobalErrorCodeConstants.NO_PLAN);
        }
        if (facilityManagerDO == null) {
            throw new ServiceException(GlobalErrorCodeConstants.NO_PLAN);
        }
        ArrangeClassesDO classesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getTempType, arrangeClasses.getTempType())
                .eqIfPresent(ArrangeClassesDO::getPatientId, arrangeClasses.getPatientId())
                .eq(0 == arrangeClasses.getTempType(), ArrangeClassesDO::getClassesTime, arrangeClasses.getClassesTime())
                .eqIfPresent(ArrangeClassesDO::getWeekState, arrangeClasses.getWeekState()));
        ArrangeClassesDO newClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getTempType, arrangeClasses.getTempType())
                .eqIfPresent(ArrangeClassesDO::getDayState, arrangeClasses.getDayState())
                .eqIfPresent(ArrangeClassesDO::getFacilityId, arrangeClasses.getFacilityId())
                .eqIfPresent(ArrangeClassesDO::getFacilitySubareaId, arrangeClasses.getFacilitySubareaId())
                .eq(0 == arrangeClasses.getTempType(), ArrangeClassesDO::getClassesTime, arrangeClasses.getClassesTime())
                .eqIfPresent(ArrangeClassesDO::getWeekState, arrangeClasses.getWeekState()));
        if (classesDO != null && 2 < classesDO.getState()) {
            throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK);
        }
        PatientDO patientDO = patientMapper.selectById(arrangeClasses.getPatientId());
        if (patientDO != null) {
            arrangeClasses.setPatientName(patientDO.getName());
            arrangeClasses.setPatientSrouce(patientDO.getPatientSource());
            arrangeClasses.setInfects(patientDO.getInfect());
            arrangeClasses.setLabels(patientDO.getLabels());
        }
        DictDataRespDTO dictData = dictDataApi.getDictData("dialyze_way", createReqVO.getDialysisValue());
        arrangeClasses.setDialysisName(dictData == null ? null : dictData.getLabel());
        FacilityDO facilityDO = facilityMapper.selectById(createReqVO.getFacilityId());
        arrangeClasses.setFacilityName(facilityDO == null ? null : facilityDO.getCode());
        FacilitySubareaDO facilitySubareaDO = subareaMapper.selectById(createReqVO.getFacilitySubareaId());
        arrangeClasses.setFaciitySubareaName(facilitySubareaDO == null ? null : facilitySubareaDO.getName());
        if (classesDO == null && newClassesDO == null) {
            if (0 != arrangeClasses.getTempType()) {
                arrangeClasses.setClassesTime(null);
            }
            arrangeClassesMapper.insert(arrangeClasses);
            TeamLogEvent teamLogEvent = new TeamLogEvent("");
            TeamLogDO teamLogDO = new TeamLogDO();
            teamLogDO.setAdjustTime(DateUtil.date());
            teamLogDO.setContent("新增");
            teamLogDO.setPatientId(arrangeClasses.getPatientId());
            teamLogDO.setPatientName(arrangeClasses.getPatientName());
            teamLogDO.setDialyzeName(arrangeClasses.getDialysisName());
            teamLogDO.setFacilityName(arrangeClasses.getFacilityName());
            Week week = DateUtil.dayOfWeekEnum(arrangeClasses.getClassesTime());
            String weekStr = week.toChinese("周");
            StringBuilder sb = new StringBuilder();
            sb.append(weekStr).append(arrangeClasses.getClassesTime() != null ? DateUtil.format(arrangeClasses.getClassesTime(), DatePattern.NORM_DATE_PATTERN) : "");
            teamLogDO.setTeamTime(sb.toString());
            DictDataRespDTO arrangeClassesPeriodTime = dictDataApi.getDictData("arrangeClassesPeriodTime", arrangeClasses.getDayState());
            teamLogDO.setWeekFlag(arrangeClassesPeriodTime != null ? arrangeClassesPeriodTime.getLabel() : null);
            teamLogEvent.setTeamLogDO(teamLogDO);
            applicationContext.publishEvent(teamLogEvent);
        } else {
            if (0 != arrangeClasses.getTempType()) {
                arrangeClasses.setClassesTime(null);
                updateClassesPosition(arrangeClasses, classesDO, newClassesDO);
            } else {
                updateClassesPosition(arrangeClasses, classesDO, newClassesDO);
            }

        }
    }

    private void updateClassesPosition(ArrangeClassesDO arrangeClasses, ArrangeClassesDO classesDO, ArrangeClassesDO newClassesDO) {
        if (classesDO == null) {
            updateClassesDO(arrangeClasses);
        } else {
            arrangeClassesMapper.deleteById(classesDO.getId());
            if (newClassesDO != null) {
                if (newClassesDO.getPatientId() == classesDO.getPatientId()) {
                    arrangeClasses.setId(null);
                    arrangeClassesMapper.insert(arrangeClasses);
                }
                updateClassesDO(arrangeClasses);
            } else {
                arrangeClasses.setId(null);
                arrangeClassesMapper.insert(arrangeClasses);
            }
        }

    }

    private void updateClassesDO(ArrangeClassesDO arrangeClasses) {
        arrangeClassesMapper.update(arrangeClasses, new LambdaUpdateWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getTempType, arrangeClasses.getTempType())
                .eq(ArrangeClassesDO::getDayState, arrangeClasses.getDayState())
                .eq(ArrangeClassesDO::getFacilityId, arrangeClasses.getFacilityId())
                .eq(ArrangeClassesDO::getFacilitySubareaId, arrangeClasses.getFacilitySubareaId())
                .eq(0 == arrangeClasses.getTempType(), ArrangeClassesDO::getClassesTime, arrangeClasses.getClassesTime())
                .eq(ArrangeClassesDO::getWeekState, arrangeClasses.getWeekState()));
    }

    @Override
    public void updateArrangeClasses(ArrangeClassesUpdateReqVO updateReqVO) {
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteArrangeClasses(ArrangeClassesCreateReqVO createReqVO) {
        long userId = StpUtil.getLoginIdAsLong();
        if (userId != 1 && createReqVO.getTempType() == 0) {
            if (createReqVO.getClassesTime().before(DateUtil.beginOfDay(new Date()))) {
                throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK_DEL);
            }
        }
        // 删除
        //ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectById(createReqVO.getId());
        //if (arrangeClassesDO != null && arrangeClassesDO.getState() > 1) {
        //    throw new ServiceException(GlobalErrorCodeConstants.DIALYSIS_NOW);
        //}
        arrangeClassesMapper.deleteById(createReqVO);
        dialysisManagerMapper.delete(new LambdaQueryWrapper<DialysisManagerDO>().eq(DialysisManagerDO::getPatientId, createReqVO.getPatientId()));
        TeamLogEvent teamLogEvent = new TeamLogEvent("");
        TeamLogDO teamLogDO = new TeamLogDO();
        teamLogDO.setAdjustTime(DateUtil.date());
        teamLogDO.setContent("删除");
        teamLogDO.setPatientId(createReqVO.getPatientId());
        teamLogDO.setPatientName(createReqVO.getPatientName());
        teamLogDO.setDialyzeName(createReqVO.getDialysisName());
        teamLogDO.setFacilityName(createReqVO.getFacilityName());
        teamLogDO.setDeptId(createReqVO.getDeptId());
        Week week = DateUtil.dayOfWeekEnum(createReqVO.getClassesTime());
        String weekStr = week.toChinese("周");
        StringBuilder sb = new StringBuilder();
        sb.append(weekStr).append(createReqVO.getClassesTime() != null ? DateUtil.format(createReqVO.getClassesTime(), DatePattern.NORM_DATE_PATTERN) : "");
        teamLogDO.setTeamTime(sb.toString());
        DictDataRespDTO arrangeClassesPeriodTime = dictDataApi.getDictData("arrangeClassesPeriodTime", createReqVO.getDayState());
        teamLogDO.setWeekFlag(arrangeClassesPeriodTime != null ? arrangeClassesPeriodTime.getLabel() : null);
        teamLogEvent.setTeamLogDO(teamLogDO);
        applicationContext.publishEvent(teamLogEvent);
    }


    @Override
    public ArrangeClassesDO getArrangeClasses(Long id) {
        return arrangeClassesMapper.selectById(id);
    }

    @Override
    public List<ArrangeClassesDO> getArrangeClassesList(Collection<Long> ids) {
        return arrangeClassesMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ArrangeClassesDO> getArrangeClassesPage(ArrangeClassesPageReqVO pageReqVO) {
        return arrangeClassesMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ArrangeClassesDO> getArrangeClassesList(ArrangeClassesCreateReqVO createReqVO) {
        return arrangeClassesMapper.selectList(createReqVO);
    }

    @Override
    public List<ArrangeClassesRespVO> tableHeadList(ArrangeClassesCreateReqVO createReqVO) {
        DateRange range = getHeadList(createReqVO);
        List<ArrangeClassesRespVO> headList = Lists.newArrayList();
        for (DateTime dateTime : range) {
            ArrangeClassesRespVO classesRespVO = new ArrangeClassesRespVO();
            int i = DateUtil.dayOfWeek(dateTime) - 1 == 0 ? 7 : DateUtil.dayOfWeek(dateTime) - 1;
            Week week = DateUtil.dayOfWeekEnum(dateTime);
            String weekStr = week.toChinese("周");
            classesRespVO.setClassesTime(dateTime);
            classesRespVO.setWeekState(i);
            classesRespVO.setWeekName(weekStr);
            headList.add(classesRespVO);
        }
        return headList;
    }

    private DateRange getHeadList(ArrangeClassesBaseVO classesBaseVO) {
        Date dateWeek = null;
        if (0 == classesBaseVO.getTempType()) {
            if (classesBaseVO.getClassesTime() == null) {
                dateWeek = DateUtil.beginOfWeek(DateUtil.date());
            } else {
                dateWeek = DateUtil.beginOfWeek(classesBaseVO.getClassesTime());
            }
        } else if (1 == classesBaseVO.getTempType()) {//模版排班
            dateWeek = DateUtil.beginOfWeek(DateUtil.date());
        } else {
            Date afterDayDate = DateUtils.getAfterDayWeek(DateUtil.endOfWeek(DateUtil.date()), classesBaseVO.getTempType() - 1);
            dateWeek = DateUtil.beginOfWeek(afterDayDate);
        }
        //计算时间周期
        DateRange range = DateUtil.range(DateUtil.beginOfWeek(dateWeek), DateUtil.endOfWeek(dateWeek), DateField.DAY_OF_WEEK);
        return range;
    }

    @Override
    public Map<Long, List<Map<String, Object>>> tableContentList(ArrangeClassesCreateReqVO createReqVO) {
        List<String> rangeList = Lists.newArrayList();
        if (createReqVO.getClassesTime() == null) {
            createReqVO.setClassesTime(DateUtil.beginOfWeek(DateUtil.date()));
        }
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getTempType, createReqVO.getTempType())
                .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getDayTime())
                .between(0 == createReqVO.getTempType(), ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime(), createReqVO.getClassesTime() != null ? DateUtil.endOfWeek(createReqVO.getClassesTime()) : createReqVO.getClassesTime())
        );
        List<ArrangeClassesRespVO> arrangeClassesRespVOS = ArrangeClassesConvert.INSTANCE.convertList(arrangeClassesDOS);
        List<FacilityDO> facilityDOS = facilityMapper.selectList();
        List<FacilityRespVO> facilityRespVOS = FacilityConvert.INSTANCE.convertList(facilityDOS);
        List<DictDataRespDTO> arrangeClassesPeriodTime = dictDataApi.getDictListData("arrangeClassesPeriodTime");
        DateRange range = getHeadList(createReqVO);
        for (DateTime dateTime : range) {
            int i = DateUtil.dayOfWeek(dateTime) - 1 == 0 ? 7 : DateUtil.dayOfWeek(dateTime) - 1;
            rangeList.add(i + "");
        }
        if (CollectionUtil.isNotEmpty(facilityRespVOS)) {
            List<Map<String, Object>> skeletonList = facilityRespVOS.stream().map(facilityRespVO -> {
                Map<String, Object> map = Maps.newHashMap();
                map.put("facilityId", facilityRespVO.getId());
                map.put("facilityName", facilityRespVO.getCode());
                map.put("facilitySubareaId", facilityRespVO.getSubareaId());
                map.put("faciitySubareaName", facilityRespVO.getSubareaName());
                for (String s : rangeList) {
                    if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
                        for (DictDataRespDTO dictDataRespDTO : arrangeClassesPeriodTime) {
                            map.put(s + dictDataRespDTO.getValue(), "&nbsp;");
                        }
                    }
                }
                FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, facilityRespVO.getId());
                if (facilityManagerDO != null) {
                    FacilityNameDO facilityNameDO = facilityNameMapper.selectById(facilityManagerDO.getFacilityNameId());
                    if (facilityNameDO != null && 1 == facilityNameDO.getState()) {
                        map.put("hemofilter", 1);
                    } else {
                        map.put("hemofilter", 0);
                    }
                    FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(facilityRespVO.getSubareaId());
                    if (facilitySubareaDO != null && 1 == facilitySubareaDO.getState()) {
                        map.put("infectious", 1);
                    } else {
                        map.put("infectious", 0);
                    }
                }
                return map;
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(arrangeClassesRespVOS)) {
                List<Map<String, Object>> skeletonListMap = skeletonList.stream().peek(map -> {
                    if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
                        for (String s : rangeList) {
                            if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
                                for (DictDataRespDTO dictDataRespDTO : arrangeClassesPeriodTime) {
                                    List<ArrangeClassesRespVO> arrangeClassesRespVOList = arrangeClassesRespVOS.stream().filter(arrangeClassesRespVO ->
                                            arrangeClassesRespVO.getDayState().equals(dictDataRespDTO.getValue())
                                                    && arrangeClassesRespVO.getWeekState() == (Integer.valueOf(s))
                                                    && arrangeClassesRespVO.getFacilityId().equals(map.get("facilityId"))
                                                    && arrangeClassesRespVO.getFacilitySubareaId().equals(map.get("facilitySubareaId"))).collect(Collectors.toList());
                                    List<ArrangeClassesRespVO> classesRespVOS = arrangeClassesRespVOList.stream().filter(arrangeClassesRespVO -> arrangeClassesRespVO.getDayState().equals(dictDataRespDTO.getValue())).collect(Collectors.toList());
                                    if (CollectionUtil.isNotEmpty(classesRespVOS)) {
                                        ArrangeClassesRespVO arrangeClassesRespVO = arrangeClassesRespVOList.stream().findFirst().get();
                                        if (arrangeClassesRespVO != null) {
                                            MPJLambdaWrapper<DialysisProtocolDO> wrapper = new MPJLambdaWrapper<>(DialysisProtocolDO.class);
                                            wrapper.leftJoin(DialyzeOptionDO.class, DialyzeOptionDO::getId, DialysisProtocolDO::getPatientDialyzeId)
                                                    .select(DialysisProtocolDO::getHemodialysisDevice, DialysisProtocolDO::getBloodFilter, DialysisProtocolDO::getPerfume)
                                                    .eq(DialysisProtocolDO::getDialyzeId, arrangeClassesRespVO.getDialysisValue())
                                                    .eq(DialysisProtocolDO::getPatientId, arrangeClassesRespVO.getPatientId())
                                                    .eq(DialysisProtocolDO::getProtocolType, 1);
                                            DialysisProtocolDO dialysisProtocolDO = dialysisProtocolMapper.selectOne(wrapper);
                                            if (dialysisProtocolDO != null) {
                                                StringBuilder sb = new StringBuilder();
                                                if (StrUtil.isNotEmpty(dialysisProtocolDO.getHemodialysisDevice())) {
                                                    sb.append(dialysisProtocolDO.getHemodialysisDevice()).append(",");
                                                }
                                                if (StrUtil.isNotEmpty(dialysisProtocolDO.getBloodFilter())) {
                                                    sb.append(dialysisProtocolDO.getBloodFilter()).append(",");
                                                }
                                                if (StrUtil.isNotEmpty(dialysisProtocolDO.getPerfume())) {
                                                    sb.append(dialysisProtocolDO.getPerfume()).append(",");
                                                }
                                                String str = sb.toString().replaceAll(",+$", "");
                                                if (StrUtil.isNotEmpty(str)) {
                                                    List<String> collect = Arrays.stream(str.split(",")).collect(Collectors.toList());
                                                    List<HisConsumablesDO> hisConsumablesDOS = hisConsumablesMapper.selectList(new LambdaQueryWrapperX<HisConsumablesDO>()
                                                            .in(HisConsumablesDO::getConsumId, collect)
                                                            .select(HisConsumablesDO::getConsumSpec));
                                                    if (CollectionUtil.isNotEmpty(hisConsumablesDOS)) {
                                                        String consumSpec = hisConsumablesDOS.stream().map(HisConsumablesDO::getConsumSpec).collect(Collectors.joining(","));
                                                        arrangeClassesRespVO.setConsumSpec(consumSpec);
                                                    }
                                                }
                                            }
                                        }
                                        map.put(s + dictDataRespDTO.getValue(), arrangeClassesRespVO);
                                    }
                                }
                            }
                        }
                    }
                }).collect(Collectors.toList());
                Map<Long, List<Map<String, Object>>> collect1 = skeletonListMap.stream().collect(Collectors.groupingBy(map -> (Long) map.get("facilitySubareaId")));
                return collect1;
            }
            Map<Long, List<Map<String, Object>>> collect = skeletonList.stream().collect(Collectors.groupingBy(map -> (Long) map.get("facilitySubareaId")));
            return collect;
        }
        return null;
    }

    @Override
    public List<String> checkPrompt(ArrangeClassesCreateReqVO createReqVO) {
        List<String> list = com.google.common.collect.Lists.newArrayList();
        PatientDO patientDO = patientMapper.selectOne(PatientDO::getId, createReqVO.getPatientId());
        if (patientDO != null) {
            FacilitySubareaDO facilitySubareaDO = subareaMapper.selectById(createReqVO.getFacilitySubareaId());
            if (StrUtil.isNotEmpty(patientDO.getInfect()) && "0".equals(facilitySubareaDO.getType())) {
                List<DictDataRespDTO> dictDataListByBatchValue = dictDataApi.getDictDataListByBatchValue(Arrays.stream(patientDO.getInfect().split(",")).collect(Collectors.toList()), "infect");
                list.add("患者有传染病(" + dictDataListByBatchValue.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(",")) + ")与该透析机不匹配，是否在此排班?");
            }
            //患者没有传染病和分区有传染病
            if (StrUtil.isEmpty(patientDO.getInfect()) && !facilitySubareaDO.getType().contains("0")) {
                list.add("患者没有传染病与该透析机不匹配,是否在此排班?");
            }
            //检测机号是否和患者的透析模式匹配
            List<FacilityManagerDO> facilityManagerDOS = facilityManagerMapper.selectList(FacilityManagerDO::getFacilityId, createReqVO.getFacilityId());
            if (CollectionUtil.isNotEmpty(facilityManagerDOS)) {
                String healModes = facilityManagerDOS.stream().map(FacilityManagerDO::getHealMode).collect(Collectors.toSet()).stream().collect(Collectors.joining(","));
                if (!healModes.contains(createReqVO.getDialysisValue())) {
                    list.add("该透析模式与设备类型不匹配,确定排班?");
                }
            }

            //如果是更新时，查询是否存在排班，存在提示是否替换
            if(createReqVO.getId() != null){
                QueryWrapper<ArrangeClassesDO> acQueryWrapper = new QueryWrapper<>();
                acQueryWrapper.lambda().eq(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
                        .eq(ArrangeClassesDO::getDayState, createReqVO.getDayState())
                        .eq(ArrangeClassesDO::getFacilityId, createReqVO.getFacilityId())
                        .ne(ArrangeClassesDO::getId, createReqVO.getId());
                Long count = arrangeClassesMapper.selectCount(acQueryWrapper);
                if(count > 0){
                    list.add("该患者存在排班，是否要替换排班?");
                }
            }

        }
        return list;
    }

    @Override
    public Integer checkUpdateStatus(ArrangeClassesCreateReqVO createReqVO) {
        return 0;
    }

    @Override
    public void updateArrangeClasses(ArrangeClassesCreateReqVO createReqVO, HttpServletRequest request) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateClasses(ArrangeClassesUpdateReqVO updateReqVO, HttpServletRequest request) {
        List<Integer> dayOfWeekList = Lists.newArrayList();
        List<Date> weekTime = Lists.newArrayList();
        DateRange range = DateUtil.range(updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime()), DateField.DAY_OF_WEEK);
        for (DateTime dateTime : range) {
            int i = DateUtil.dayOfWeek(dateTime) - 1 == 0 ? 7 : DateUtil.dayOfWeek(dateTime) - 1;
            dayOfWeekList.add(i);
            weekTime.add(dateTime);
        }

        DateTime currentWeek = DateUtil.beginOfWeek(DateUtil.date());
        int compare = DateUtil.compare(updateReqVO.getClassesTime(), currentWeek);
        if (0 == compare) {
            int i = DateUtil.dayOfWeek(DateUtil.date());
            List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                    .eqIfPresent(ArrangeClassesDO::getTempType, updateReqVO.getTempType())
                    .betweenIfPresent(ArrangeClassesDO::getWeekState, i, 7));
            if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
                for (ArrangeClassesDO arrangeClassesDO : arrangeClassesDOS) {
                    ArrangeClassesDO classesDO = getOldClasses(updateReqVO, arrangeClassesDO);
                    if (classesDO == null) {
                        ArrangeClassesDO classesDO1 = getOldClassesByPatientId(updateReqVO, arrangeClassesDO);
                        if (classesDO1 != null) {
                            continue;
                        }
                        arrangeClassesDO.setId(null);
                        arrangeClassesDO.setTempType(0);
                        for (Integer week : dayOfWeekList) {
                            if (arrangeClassesDO.getWeekState() == week) {
                                arrangeClassesDO.setClassesTime(weekTime.get(week - 1));
                                break;
                            }
                        }
                        arrangeClassesMapper.insert(arrangeClassesDO);
                    } else {
                        ArrangeClassesDO classesDO1 = getOldClassesByPatientId(updateReqVO, arrangeClassesDO);
                        if (classesDO1 != null) {
                            continue;
                        }
                        arrangeClassesDO.setId(null);
                        arrangeClassesDO.setTempType(0);
                        for (Integer week : dayOfWeekList) {
                            if (arrangeClassesDO.getWeekState() == week) {
                                arrangeClassesDO.setClassesTime(weekTime.get(week - 1));
                                break;
                            }
                        }
                        updateClasses(updateReqVO, arrangeClassesDO);
                    }
                }
            }
        } else if (updateReqVO.getClassesTime().after(DateUtil.beginOfWeek(DateUtil.date()))) {
            List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                    .eqIfPresent(ArrangeClassesDO::getTempType, updateReqVO.getTempType())
                    .betweenIfPresent(ArrangeClassesDO::getWeekState, 1, 7));
            if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
                List<ArrangeClassesDO> arrangeClassesDOS1 = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                        .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime())));
                if (CollectionUtil.isNotEmpty(arrangeClassesDOS1)) {
                    for (ArrangeClassesDO arrangeClassesDO : arrangeClassesDOS) {
                        ArrangeClassesDO classesDO = getOldClasses(updateReqVO, arrangeClassesDO);
                        if (classesDO == null) {
                            ArrangeClassesDO classesDO1 = getOldClassesByPatientId(updateReqVO, arrangeClassesDO);
                            if (classesDO1 != null) {
                                continue;
                            }
                            arrangeClassesDO.setId(null);
                            arrangeClassesDO.setTempType(0);
                            for (Integer week : dayOfWeekList) {
                                if (arrangeClassesDO.getWeekState() == week) {
                                    arrangeClassesDO.setClassesTime(weekTime.get(week - 1));
                                    break;
                                }
                            }
                            arrangeClassesMapper.insert(arrangeClassesDO);
                        } else {
                            ArrangeClassesDO classesDO1 = getOldClassesByPatientId(updateReqVO, arrangeClassesDO);
                            if (classesDO1 != null) {
                                continue;
                            }
                            arrangeClassesDO.setId(null);
                            arrangeClassesDO.setTempType(0);
                            for (Integer week : dayOfWeekList) {
                                if (arrangeClassesDO.getWeekState() == week) {
                                    arrangeClassesDO.setClassesTime(weekTime.get(week - 1));
                                    break;
                                }
                            }
                            updateClasses(updateReqVO, arrangeClassesDO);
                        }
                    }
                } else {
                    List<ArrangeClassesDO> collect = arrangeClassesDOS.stream().peek(arrangeClassesDO -> {
                        arrangeClassesDO.setId(null);
                        arrangeClassesDO.setTempType(0);
                        for (Integer week : dayOfWeekList) {
                            if (arrangeClassesDO.getWeekState() == week) {
                                arrangeClassesDO.setClassesTime(weekTime.get(week - 1));
                            }
                        }
                    }).collect(Collectors.toList());
                    arrangeClassesMapper.insertBatch(collect);
                }
            }
        }


    }

    @Override
    public void importMBClasses(ArrangeClassesUpdateReqVO updateReqVO, HttpServletRequest request) {

    }

    private void updateClasses(ArrangeClassesUpdateReqVO updateReqVO, ArrangeClassesDO arrangeClassesDO) {
        arrangeClassesMapper.update(arrangeClassesDO, new LambdaUpdateWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getWeekState, arrangeClassesDO.getWeekState())
                .eq(ArrangeClassesDO::getDayState, arrangeClassesDO.getDayState())
                .eq(ArrangeClassesDO::getFacilityId, arrangeClassesDO.getFacilityId())
                .eq(ArrangeClassesDO::getFacilitySubareaId, arrangeClassesDO.getFacilitySubareaId())
                .eq(ArrangeClassesDO::getTempType, 0)
                .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime())));
    }

    private ArrangeClassesDO getOldClassesByPatientId(ArrangeClassesUpdateReqVO updateReqVO, ArrangeClassesDO arrangeClassesDO) {
        ArrangeClassesDO classesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getWeekState, arrangeClassesDO.getWeekState())
                .eqIfPresent(ArrangeClassesDO::getPatientId, arrangeClassesDO.getPatientId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime())));
        return classesDO;
    }

    private ArrangeClassesDO getOldClasses(ArrangeClassesUpdateReqVO updateReqVO, ArrangeClassesDO arrangeClassesDO) {
        ArrangeClassesDO classesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getWeekState, arrangeClassesDO.getWeekState())
                .eqIfPresent(ArrangeClassesDO::getDayState, arrangeClassesDO.getDayState())
                .eqIfPresent(ArrangeClassesDO::getFacilityId, arrangeClassesDO.getFacilityId())
                .eqIfPresent(ArrangeClassesDO::getFacilitySubareaId, arrangeClassesDO.getFacilitySubareaId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                .between(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime(), DateUtil.endOfWeek(updateReqVO.getClassesTime())));
        return classesDO;
    }

    @Override
    public Map<String, Object> getCalendars(ArrangeClassesCreateReqVO createReqVO) {
        PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
        Map<String, Object> map = Maps.newHashMap();
        List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPatientId, createReqVO.getPatientId());
        if (CollectionUtil.isNotEmpty(dialyzeOptionDOS)) {
            String dialyze = dialyzeOptionDOS.stream().map(dialyzeOptionDO -> {
                DictDataRespDTO pinlv = dictDataApi.getDictData("zhouqi", dialyzeOptionDO.getFrequencyDictValue());
                DictDataRespDTO cycleDay = dictDataApi.getDictData("cycle_day", dialyzeOptionDO.getCycleDictValue());
                DictDataRespDTO cishu = dictDataApi.getDictData("cishu", dialyzeOptionDO.getNumberDictValue());
                DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", dialyzeOptionDO.getDialyzeDictValue());
                StringBuilder sb = new StringBuilder();
                if (StrUtil.isNotEmpty(dialyzeOptionDO.getCycleDictValue())) {
                    sb.append(dialyzeWay == null ? "" : dialyzeWay.getLabel())
                            .append("(")
                            .append(pinlv == null ? "" : pinlv.getLabel())
                            .append(cycleDay == null ? "" : cycleDay.getLabel())
                            .append(")");
                }
                if (StrUtil.isNotEmpty(dialyzeOptionDO.getNumberDictValue())) {
                    sb.append(dialyzeWay == null ? "" : dialyzeWay.getLabel())
                            .append("(")
                            .append(pinlv == null ? "" : pinlv.getLabel())
                            .append(cishu == null ? "" : cishu.getLabel())
                            .append(")");
                }
                return sb.toString();
            }).collect(Collectors.joining("、"));
            map.put("dialyze", dialyze);
        } else {
            map.put("dialyze", null);
        }
        Date beginDate = null;
        Date endDate = null;
        if (createReqVO.getMonth() == null) {
            beginDate = DateUtil.beginOfMonth(DateUtil.date());
            endDate = DateUtil.endOfMonth(DateUtil.date());
        } else {
            DateTime parse = DateUtil.parse(createReqVO.getMonth(), DatePattern.NORM_MONTH_PATTERN);
            beginDate = DateUtil.beginOfMonth(parse);
            endDate = DateUtil.endOfMonth(parse);
        }
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eq(ArrangeClassesDO::getTempType, "0")
                .between(ArrangeClassesDO::getClassesTime, beginDate, endDate));
        List<ArrangeClassesRespVO> arrangeClassesRespVOList = ArrangeClassesConvert.INSTANCE.convertList(arrangeClassesDOS);
        if (CollectionUtil.isNotEmpty(arrangeClassesRespVOList)) {
            arrangeClassesRespVOList = arrangeClassesRespVOList.stream().peek(arrangeClassesRespVO -> {
                if (patientDO != null) {
                    DictDataRespDTO patientSource = dictDataApi.getDictData("patient_source", patientDO.getPatientSource());
                    arrangeClassesRespVO.setPatientSrouce(patientSource.getLabel());
                }
            }).collect(Collectors.toList());
        }
        map.put("teamPatientDOS", arrangeClassesRespVOList);
        return map;
    }

    @Override
    public List<ArrangeClassesRespVO> patientList(ArrangeClassesCreateReqVO createVO) {
        MPJLambdaWrapper<ArrangeClassesDO> wrapper = new MPJLambdaWrapper<>(ArrangeClassesDO.class);
        wrapper.leftJoin(PatientDO.class, PatientDO::getId, ArrangeClassesDO::getPatientId)
                .selectAll(ArrangeClassesDO.class)
                .select(PatientDO::getDialyzeNo, PatientDO::getName, PatientDO::getSex, PatientDO::getAge, PatientDO::getEndemicArea
                        , PatientDO::getBedNo, PatientDO::getInitDialyzeNo, PatientDO::getFirstReceiveTime, PatientDO::getEnterWay, PatientDO::getSpellName)
                .eq(ArrangeClassesDO::getClassesTime, createVO.getClassesTime())
                .eq(ArrangeClassesDO::getTempType, 0)
                .eq(createVO.getDayState() != null, ArrangeClassesDO::getDayState, createVO.getDayState())
                .in(CollectionUtil.isNotEmpty(createVO.getFacilitySubareaIds()), ArrangeClassesDO::getFacilitySubareaId, createVO.getFacilitySubareaIds())
                .and(StrUtil.isNotEmpty(createVO.getMore()), i -> i
                        .like(PatientDO::getName, createVO.getMore())
                        .or()
                        .like(PatientDO::getSpellName, createVO.getMore())
                        .or()
                        .like(PatientDO::getDialyzeNo, createVO.getMore())).orderByAsc(ArrangeClassesDO::getFacilityId);
        List<ArrangeClassesRespVO> arrangeClassesRespVOList = arrangeClassesMapper.selectJoinList(ArrangeClassesRespVO.class, wrapper);
        if (CollectionUtil.isNotEmpty(arrangeClassesRespVOList)) {
            arrangeClassesRespVOList = arrangeClassesRespVOList.stream().peek(arrangeClassesRespVO -> {
                //干体重
                List<DryWeightDO> dryWeightDOS = dryWeightMapper.selectList(new LambdaQueryWrapperX<DryWeightDO>()
                        .eqIfPresent(DryWeightDO::getPatientId, arrangeClassesRespVO.getPatientId())
                        .orderByDesc(DryWeightDO::getId));
                if (CollectionUtil.isNotEmpty(dryWeightDOS)) {
                    DryWeightDO dryWeightDO = dryWeightDOS.stream().findFirst().get();
                    arrangeClassesRespVO.setDryWeight(StrUtil.isNotEmpty(dryWeightDO.getDryWeight()) ? dryWeightDO.getDryWeight() : null);
                }
                List<DiseaseReasonDO> diseaseReasonDOS = diseaseReasonMapper.selectList(DiseaseReasonDO::getPatientId, arrangeClassesRespVO.getPatientId(), DiseaseReasonDO::getSync, 1);
                if (CollectionUtil.isNotEmpty(diseaseReasonDOS)) {
                    String diseaseReasonNames = diseaseReasonDOS.stream().map(diseaseReasonDO -> {
                        return StrUtil.isNotEmpty(diseaseReasonDO.getParentTwoName()) ? diseaseReasonDO.getParentTwoName() : "";
                    }).collect(Collectors.joining(","));
                    arrangeClassesRespVO.setDiseaseReasonNames(diseaseReasonNames);
                }
                FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, arrangeClassesRespVO.getFacilityId());
                arrangeClassesRespVO.setEquipmentId(facilityManagerDO != null ? facilityManagerDO.getFacilityNameId() : null);
                arrangeClassesRespVO.setEquipmentType(facilityManagerDO != null ? facilityManagerDO.getFacilityTypeId() : null);
            }).collect(Collectors.toList());
        }
        return arrangeClassesRespVOList;
    }

    @Override
    public Map<String, Object> dialyzBudgePage(ArrangeClassesCreateReqVO createVO) {
        Map<String, Object> map = Maps.newHashMap();
        List<String> list2 = Lists.newArrayList();
        Date beginDate = null;
        Date endDate = null;
        if (createVO.getStartTime() == null && createVO.getEndTime() == null) {
            beginDate = DateUtil.beginOfMonth(DateUtil.date());
            endDate = DateUtil.endOfMonth(DateUtil.date());
        } else {
            beginDate = createVO.getStartTime();
            endDate = createVO.getEndTime();
        }
        List<DialyzBudgetRespVO> budgetRespVOS = null;
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getTempType, 0)
                .between(ArrangeClassesDO::getClassesTime, beginDate, endDate)
                .like(StrUtil.isNotEmpty(createVO.getPatientName()), ArrangeClassesDO::getPatientName, createVO.getPatientName()));
        if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
            Set<Long> patientIds = arrangeClassesDOS.stream().map(ArrangeClassesDO::getPatientId).collect(Collectors.toSet());
            Date finalBeginDate = beginDate;
            Date finalEndDate = endDate;
            budgetRespVOS = patientIds.stream().map(patientId -> {
                List<String> list = com.google.common.collect.Lists.newArrayList();
                PatientDO patientDO = patientMapper.selectById(patientId);
                DialyzBudgetRespVO budgetRespVO = new DialyzBudgetRespVO();
                List<ArrangeClassesDO> arrangeClassesDOList = arrangeClassesMapper.selectList(new LambdaQueryWrapper<ArrangeClassesDO>()
                        .eq(ArrangeClassesDO::getTempType, 0)
                        .between(ArrangeClassesDO::getClassesTime, finalBeginDate, finalEndDate)
                        .eq(ArrangeClassesDO::getPatientId, patientId));
                String content = arrangeClassesDOList.stream().map(arrangeClassesDO -> {
                    return arrangeClassesDO.getDialysisName() + "【" + DateUtil.format(arrangeClassesDO.getClassesTime(), "MM-dd") + "】";
                }).collect(Collectors.joining("、"));
                if (patientDO != null) {
                    budgetRespVO.setPatientName(patientDO.getName());
                    budgetRespVO.setDialyzNo(patientDO.getDialyzeNo());
                }
                budgetRespVO.setContent(content);
                List<ArrangeClassesDO> patientDOList = arrangeClassesMapper.selectList(new LambdaQueryWrapper<ArrangeClassesDO>()
                        .eq(ArrangeClassesDO::getPatientId, patientId)
                        .eq(ArrangeClassesDO::getTempType, 0)
                        .between(ArrangeClassesDO::getClassesTime, finalBeginDate, finalEndDate)
                        .like(StrUtil.isNotEmpty(createVO.getPatientName()), ArrangeClassesDO::getPatientName, createVO.getPatientName()));
                if (CollectionUtil.isNotEmpty(patientDOList)) {
                    Set<String> dialyzeNameSet = patientDOList.stream().map(ArrangeClassesDO::getDialysisName).collect(Collectors.toSet());
                    for (String s : dialyzeNameSet) {
                        Long count = arrangeClassesMapper.selectCount(new LambdaQueryWrapper<ArrangeClassesDO>()
                                .eq(ArrangeClassesDO::getDialysisName, s)
                                .eq(ArrangeClassesDO::getPatientId, patientId)
                                .eq(ArrangeClassesDO::getTempType, 0)
                                .between(ArrangeClassesDO::getClassesTime, finalBeginDate, finalEndDate)
                                .like(StrUtil.isNotEmpty(createVO.getPatientName()), ArrangeClassesDO::getPatientName, createVO.getPatientName()));
                        list.add(s + "【" + count + "次】");
                    }
                    String collect = list.stream().collect(Collectors.joining("、"));
                    budgetRespVO.setTotal(collect);
                }
                return budgetRespVO;
            }).collect(Collectors.toList());
            Set<String> dialyzeNames = arrangeClassesDOS.stream().map(ArrangeClassesDO::getDialysisName).collect(Collectors.toSet());
            for (String dialyzeName : dialyzeNames) {
                Long count = arrangeClassesMapper.selectCount(new LambdaQueryWrapper<ArrangeClassesDO>()
                        .eq(ArrangeClassesDO::getDialysisName, dialyzeName)
                        .eq(ArrangeClassesDO::getTempType, 0)
                        .between(ArrangeClassesDO::getClassesTime, beginDate, endDate)
                        .like(StrUtil.isNotEmpty(createVO.getPatientName()), ArrangeClassesDO::getPatientName, createVO.getPatientName()));
                list2.add(dialyzeName + "【" + count + "次】");
            }
            String rowsTotal = list2.stream().collect(Collectors.joining("、"));
            map.put("rowsTotal", rowsTotal);
        }
        map.put("budgetRespVOS", budgetRespVOS);
        return map;
    }

    @Override
    public Map<String, Object> teamPatientInfo(Long patientId) {
        Map<String, Object> map = Maps.newHashMap();
        //透析方案信息
        List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPatientId, patientId);
        if (CollectionUtil.isNotEmpty(dialyzeOptionDOS)) {
            String dialyze = dialyzeOptionDOS.stream().map(dialyzeOptionDO -> {
                DictDataRespDTO pinlv = dictDataApi.getDictData("zhouqi", dialyzeOptionDO.getFrequencyDictValue());
                DictDataRespDTO cycleDay = dictDataApi.getDictData("cycle_day", dialyzeOptionDO.getCycleDictValue());
                DictDataRespDTO cishu = dictDataApi.getDictData("cishu", dialyzeOptionDO.getNumberDictValue());
                DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", dialyzeOptionDO.getDialyzeDictValue());
                StringBuilder sb = new StringBuilder();
                //频次
                if (StrUtil.isNotEmpty(dialyzeOptionDO.getCycleDictValue())) {
                    sb.append(dialyzeWay == null ? "" : dialyzeWay.getLabel())
                            .append("(")
                            .append(pinlv == null ? "" : pinlv.getLabel())
                            .append(cycleDay == null ? "" : cycleDay.getLabel())
                            .append(")");
                }
                if (StrUtil.isNotEmpty(dialyzeOptionDO.getNumberDictValue())) {
                    sb.append(dialyzeWay == null ? "" : dialyzeWay.getLabel())
                            .append("(")
                            .append(pinlv == null ? "" : pinlv.getLabel())
                            .append(cishu == null ? "" : cishu.getLabel())
                            .append(")");
                }
                return sb.toString();
            }).collect(Collectors.joining("、"));
            map.put("dialyze", dialyze);
        } else {
            map.put("dialyze", "");
        }
        UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
        createReqDTO.setId(StpUtil.getLoginIdAsLong());
        UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
        if (adminUser != null && !"0".equals(adminUser.getTempNumber())) {
            for (Integer i = 1; i <= Integer.valueOf(adminUser.getTempNumber()); i++) {
                List<DrainTeamAlreadyVO> teamAlreadyVOS = com.google.common.collect.Lists.newArrayList();
                List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getTempType, i)
                        .eqIfPresent(ArrangeClassesDO::getPatientId, patientId));
                teamAlreadyVOS = weekStateList(arrangeClassesDOS, teamAlreadyVOS);
                map.put("temp" + i, teamAlreadyVOS);
            }
        }
        return map;
    }

    @Override
    public List<ArrangeClassesRespVO> getMonthCalendars(ArrangeClassesCreateReqVO createReqVO) {
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfMonth(createReqVO.getDayTime()), DateUtil.endOfMonth(createReqVO.getDayTime())));
        return ArrangeClassesConvert.INSTANCE.convertList(arrangeClassesDOS);
    }

    @Override
    public Map<String, Object> tempPush(ArrangeClassesCreateReqVO createReqVO,HttpServletRequest request) {
        Map<String, Object> map = Maps.newHashMap();
        DateTime dateTime = DateUtil.offsetWeek(new Date(), 3);
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfWeek(new Date()), DateUtil.endOfWeek(dateTime))
                .notIn(ArrangeClassesDO::getTempType, 0));
        for (int i = 1; i <= 4; i++) {
            DrainTeamAlreadyVO teamAlreadyVO = new DrainTeamAlreadyVO();
            int finalI = i;
            if (1 == i) {
                teamAlreadyVO.setClassesTime("本周");
            } else {
                DateTime dateTime1 = DateUtil.offsetWeek(new Date(), i - 1);
                teamAlreadyVO.setClassesTime(DateUtil.beginOfWeek(dateTime1).toString(DatePattern.NORM_DATE_PATTERN));
            }
            if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
                List<ArrangeClassesDO> arrangeClassesDOList = arrangeClassesDOS.stream().filter(arrangeClassesDO -> finalI == arrangeClassesDO.getTempType()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(arrangeClassesDOList)) {
                    for (ArrangeClassesDO arrangeClassesDO : arrangeClassesDOList) {
                        switch (arrangeClassesDO.getWeekState()) {
                            case 1:
                                teamAlreadyVO.setMonday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                break;
                            case 2:
                                teamAlreadyVO.setTuesday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                break;
                            case 3:
                                teamAlreadyVO.setWednesday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                break;
                            case 4:
                                teamAlreadyVO.setThursday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                break;
                            case 5:
                                teamAlreadyVO.setFriday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                break;
                            case 6:
                                teamAlreadyVO.setSaturday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                break;
                            case 7:
                                teamAlreadyVO.setSunday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                break;
                        }
                    }
                }
            }
            map.put(i + "", teamAlreadyVO);
        }
        List<String> collect = Arrays.stream(createReqVO.getAdviceIds().split(",")).collect(Collectors.toList());
        DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectOne(DialysisAdviceDO::getId, collect.get(0));
        if (dialysisAdviceDO != null) {
            Map<String, Object> map2 = Maps.newHashMap();
            map2.put("startTime", dialysisAdviceDO.getStartTime() != null ? DateUtil.format(dialysisAdviceDO.getStartTime(), DatePattern.NORM_TIME_PATTERN) : null);
            map2.put("frequency", dialysisAdviceDO.getFrequency());
            map2.put("medicateState", dialysisAdviceDO.getMedicateState());
            map.put("pushInfo", map2);
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void brainPower(List<ArrangeClassesCreateReqVO> createReqVO,HttpServletRequest request) {
        for (ArrangeClassesCreateReqVO arrangeClassesCreateReqVO : createReqVO) {
            ArrangeClassesDO classesDO = ArrangeClassesConvert.INSTANCE.convert(arrangeClassesCreateReqVO);
            if (classesDO.getFacilityId() == null) {
                arrangeClassesMapper.delete(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getTempType, classesDO.getTempType())
                        .eqIfPresent(ArrangeClassesDO::getWeekState, classesDO.getWeekState())
                        .eqIfPresent(ArrangeClassesDO::getPatientId, classesDO.getPatientId())
                        .eqIfPresent(ArrangeClassesDO::getFacilityId, classesDO.getFacilityId())
                        .eqIfPresent(ArrangeClassesDO::getFacilitySubareaId, classesDO.getFacilitySubareaId())
                        .between(ArrangeClassesDO::getClassesTime, classesDO.getClassesTime(), DateUtil.endOfWeek(classesDO.getClassesTime())));
                continue;
            }
            List<DisinfectionPlanDO> disinfectionPlanDOS = disinfectionPlanMapper.selectList(new LambdaQueryWrapperX<DisinfectionPlanDO>()
                    .eqIfPresent(DisinfectionPlanDO::getFacilityId, classesDO.getFacilityId())
                    .eqIfPresent(DisinfectionPlanDO::getWeekDay, classesDO.getDayState())
                    .eqIfPresent(DisinfectionPlanDO::getDisinfectionTime, classesDO.getWeekState()));
            FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, classesDO.getFacilityId());
            if (CollectionUtil.isEmpty(disinfectionPlanDOS) || facilityManagerDO == null) {
                throw new ServiceException(GlobalErrorCodeConstants.NO_PLAN);
            }
            if (facilityManagerDO == null) {
                throw new ServiceException(GlobalErrorCodeConstants.NO_PLAN);
            }
            FacilityDO facilityDO = facilityMapper.selectById(arrangeClassesCreateReqVO.getFacilityId());
            FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(arrangeClassesCreateReqVO.getFacilitySubareaId());
            PatientDO patientDO = patientMapper.selectById(arrangeClassesCreateReqVO.getPatientId());
            classesDO.setFacilityName(facilityDO != null ? facilityDO.getCode() : null);
            classesDO.setFaciitySubareaName(facilitySubareaDO != null ? facilitySubareaDO.getName() : null);
            if (patientDO != null) {
                classesDO.setLabels(patientDO.getLabels());
                classesDO.setPatientSrouce(patientDO.getPatientSource());
                classesDO.setInfects(patientDO.getInfect());
            }
            DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", classesDO.getDialysisValue());
            classesDO.setDialysisName(dialyzeWay != null ? dialyzeWay.getLabel() : null);
            ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                    .eqIfPresent(ArrangeClassesDO::getTempType, classesDO.getTempType())
                    .eqIfPresent(ArrangeClassesDO::getWeekState, classesDO.getWeekState())
                    .eqIfPresent(ArrangeClassesDO::getPatientId, classesDO.getPatientId())
                    .between(ArrangeClassesDO::getClassesTime, classesDO.getClassesTime(), DateUtil.endOfWeek(classesDO.getClassesTime())));
            if (arrangeClassesDO != null) {
                arrangeClassesMapper.update(classesDO, new LambdaUpdateWrapper<ArrangeClassesDO>()
                        .eq(ArrangeClassesDO::getTempType, classesDO.getTempType())
                        .eq(ArrangeClassesDO::getWeekState, classesDO.getWeekState())
                        .eq(ArrangeClassesDO::getPatientId, classesDO.getPatientId())
                        .between(ArrangeClassesDO::getClassesTime, classesDO.getClassesTime(), DateUtil.endOfWeek(classesDO.getClassesTime())));
            } else {
                classesDO.setClassesTime(null);
                arrangeClassesMapper.insert(classesDO);
            }
        }
    }

    @Override
    public Map<String, Object> brainPowerList(ArrangeClassesCreateReqVO createReqVO) {
        PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
        Map<String, Object> map = Maps.newHashMap();
        for (int i = 1; i <= 4; i++) {
            List<BrainPowerCreateReqVO> list = Lists.newArrayList();
            List<Integer> weekStates = Lists.newArrayList();
            Map<Integer, Date> dateMap = Maps.newHashMap();
            BrainPowerRespVO brainPowerRespVO = new BrainPowerRespVO();
            Date startWeek = null;
            Date endWeek = null;
            if (1 == i) {
                startWeek = DateUtil.beginOfWeek(new Date());
                endWeek = DateUtil.endOfWeek(DateUtil.offsetWeek(new Date(), i - 1));
            } else {
                DateTime dateTime = DateUtil.offsetWeek(new Date(), i - 1);
                startWeek = DateUtil.beginOfWeek(dateTime);
                endWeek = DateUtil.endOfWeek(dateTime);
            }
            DateRange range = DateUtil.range(startWeek, endWeek, DateField.DAY_OF_WEEK);
            for (DateTime dateTime : range) {
                int w = DateUtil.dayOfWeek(dateTime) - 1 == 0 ? 7 : DateUtil.dayOfWeek(dateTime) - 1;
                dateMap.put(w, dateTime);
            }
            for (int j = 1; j <= 7; j++) {
                BrainPowerCreateReqVO createReqVO1 = new BrainPowerCreateReqVO();
                for (Map.Entry<Integer, Date> integerDateEntry : dateMap.entrySet()) {
                    if (j == integerDateEntry.getKey()) {
                        createReqVO1.setClassesTime(DateUtil.beginOfDay(integerDateEntry.getValue()));
                        Week week = DateUtil.dayOfWeekEnum(integerDateEntry.getValue());
                        String weekStr = week.toChinese("周");
                        createReqVO1.setWeekName(weekStr);
                    }
                }
                ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                        .betweenIfPresent(ArrangeClassesDO::getClassesTime, startWeek, endWeek)
                        .eqIfPresent(ArrangeClassesDO::getTempType, i)
                        .eqIfPresent(ArrangeClassesDO::getWeekState, j));
                if (arrangeClassesDO != null) {
                    BeanUtil.copyProperties(arrangeClassesDO, createReqVO1);
                    createReqVO1.setDisabled(1);
                    List<FacilityDO> facilityDOS = facilityMapper.selectList(FacilityDO::getSubareaId, arrangeClassesDO.getFacilitySubareaId());
                    if (CollectionUtil.isNotEmpty(facilityDOS)) {
                        createReqVO1.setFacilityList(facilityDOS);
                    }
                    weekStates.add(j);
                    createReqVO1.setWeekState(j);
                    createReqVO1.setDayState(arrangeClassesDO.getDayState());
                    createReqVO1.setDialysisValue(arrangeClassesDO.getDialysisValue());
                    createReqVO1.setPatientId(createReqVO.getPatientId());
                    createReqVO1.setPatientName(patientDO != null ? patientDO.getName() : null);
                    list.add(createReqVO1);
                }
            }
            brainPowerRespVO.setParams(list);
            brainPowerRespVO.setWeekStates(weekStates);
            map.put(i + "", brainPowerRespVO);
        }
        return map;
    }

    @Override
    public List<FacilityRespVO> getClassFacilityList(ArrangeClassesCreateReqVO createReqVO) {
        createReqVO.setPatientId(null);
        List<FacilityDO> facilityDOS = facilityMapper.selectList(FacilityDO::getSubareaId, createReqVO.getFacilitySubareaId());
        List<FacilityRespVO> facilityRespVOS = FacilityConvert.INSTANCE.convertList(facilityDOS);
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getTempType, createReqVO.getTempType())
                .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
                .eqIfPresent(ArrangeClassesDO::getWeekState, createReqVO.getWeekState())
                .eqIfPresent(ArrangeClassesDO::getDayState, createReqVO.getDayState())
                .eqIfPresent(ArrangeClassesDO::getFacilitySubareaId, createReqVO.getFacilitySubareaId()));
        if (CollectionUtil.isNotEmpty(facilityRespVOS)) {
            if (CollectionUtil.isEmpty(arrangeClassesDOS)) {
                return facilityRespVOS;
            }
            List<Long> facilityIds = arrangeClassesDOS.stream().map(ArrangeClassesDO::getFacilityId).collect(Collectors.toList());
            return facilityRespVOS.stream().filter(facilityRespVO -> !facilityIds.contains(facilityRespVO.getId())).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public ArrangeClassesRespVO getTeamPatient(ArrangeClassesCreateReqVO createReqVO) {
        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getClassesTime())
                .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0));
        return ArrangeClassesConvert.INSTANCE.convert(arrangeClassesDO);
    }

    @Override
    public PageResult<ArrangeClassesRespVO> getHistoryPage(ArrangeClassesPageReqVO pageReqVO) {
        PageResult<ArrangeClassesRespVO> classesRespVOPageResult = new PageResult<>();
        pageReqVO.setPageNo((pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());
        List<ArrangeClassesRespVO> arrangeClassesRespVOList = arrangeClassesMapper.selectPageHistory(pageReqVO);
        Long count = arrangeClassesMapper.selectCountHistory(pageReqVO);
        classesRespVOPageResult.setList(arrangeClassesRespVOList);
        if (CollectionUtil.isNotEmpty(classesRespVOPageResult.getList())) {
            List<ArrangeClassesRespVO> collect = classesRespVOPageResult.getList().stream().peek(arrangeClassesRespVO -> {
                int week = DateUtil.weekOfYear(DateUtil.beginOfWeek(arrangeClassesRespVO.getClassesTime()));
                String format = DateUtil.format(arrangeClassesRespVO.getClassesTime(), DatePattern.NORM_DATE_PATTERN);
                Week weekEnum = DateUtil.dayOfWeekEnum(arrangeClassesRespVO.getClassesTime());
                String weekStr = weekEnum.toChinese("周");
                arrangeClassesRespVO.setClassesTimeStr(week + "周" + "(" + format + ")" + weekStr);
            }).collect(Collectors.toList());
            classesRespVOPageResult.setList(collect);
        }
        classesRespVOPageResult.setTotal(count);
        return classesRespVOPageResult;
    }

    @Override
    public void updateDeleted(ArrangeClassesUpdateReqVO updateReqVO) {
        Long count = arrangeClassesMapper.selectCount(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getWeekState, updateReqVO.getWeekState())
                .eq(ArrangeClassesDO::getDayState, updateReqVO.getDayState())
                .eq(ArrangeClassesDO::getClassesTime, updateReqVO.getClassesTime())
                .eq(ArrangeClassesDO::getFacilityId, updateReqVO.getFacilityId())
                .eq(ArrangeClassesDO::getFacilitySubareaId, updateReqVO.getFacilitySubareaId()));
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.HISTORY_WEEK_EXIT_PATIENT);
        }
        arrangeClassesMapper.updateDeleted(updateReqVO);
    }

    @Override
    public Map<String, Object> getPatientNum(String date) {
        return null;
    }

    private List<DrainTeamAlreadyVO> weekStateList(List<ArrangeClassesDO> arrangeClassesDOS, List<DrainTeamAlreadyVO> teamAlreadyVOS) {
        List<DictDataRespDTO> arrangeClassesPeriodTime = dictDataApi.getDictListData("arrangeClassesPeriodTime");
        if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
            for (DictDataRespDTO dictDataRespDTO : arrangeClassesPeriodTime) {
                DrainTeamAlreadyVO drainTeamAlreadyVO = new DrainTeamAlreadyVO();
                drainTeamAlreadyVO.setDayState(dictDataRespDTO.getLabel());
                if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
                    List<ArrangeClassesDO> mornings = arrangeClassesDOS.stream().filter(arrangeClassesDO -> dictDataRespDTO.getValue().equals(arrangeClassesDO.getDayState())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(mornings)) {
                        for (ArrangeClassesDO arrangeClassesDO : mornings) {
                            switch (arrangeClassesDO.getWeekState()) {
                                case 1:
                                    drainTeamAlreadyVO.setMonday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 2:
                                    drainTeamAlreadyVO.setTuesday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 3:
                                    drainTeamAlreadyVO.setWednesday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 4:
                                    drainTeamAlreadyVO.setThursday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 5:
                                    drainTeamAlreadyVO.setFriday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 6:
                                    drainTeamAlreadyVO.setSaturday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                                case 7:
                                    drainTeamAlreadyVO.setSunday(StrUtil.isEmpty(arrangeClassesDO.getDialysisName()) ? null : arrangeClassesDO.getDialysisName());
                                    break;
                            }
                        }
                    }
                }
                teamAlreadyVOS.add(drainTeamAlreadyVO);
            }
        }
        return teamAlreadyVOS;
    }
}
