package com.thj.boot.module.business.controller.admin.teampatient;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesCreateReqVO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesRespVO;
import com.thj.boot.module.business.service.arrangeclasses.ArrangeClassesService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2024/1/9 16:55
 * @description
 */
@RestController
@RequestMapping("/business/teamPatient")
@ConditionalOnProperty(name = "spring.redis.enabled", havingValue = "true", matchIfMissing = true)
public class TeamPatientController {

    @Resource
    private ArrangeClassesService arrangeClassesService;

    /**
     * 排班详情-app
     */
    @PostMapping("/get")
    public CommonResult<ArrangeClassesRespVO> getTeamPatient(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        return success(arrangeClassesService.getTeamPatient(createReqVO));
    }
}
